/**
 * Configuração do Jest para testes do sistema de geração de conteúdo SEO
 */

module.exports = {
  // Ambiente de teste
  testEnvironment: 'node',

  // Padrões de arquivos de teste
  testMatch: [
    '<rootDir>/src/tests/**/*.test.ts',
    '<rootDir>/src/tests/**/*.test.js'
  ],

  // Extensões de arquivo suportadas
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json'],

  // Transformações
  transform: {
    '^.+\\.(ts|tsx)$': 'ts-jest'
  },

  // Configuração do TypeScript
  preset: 'ts-jest',

  // Mapeamento de módulos (para resolver imports com @/)
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1'
  },

  // Arquivos de setup
  setupFilesAfterEnv: ['<rootDir>/src/tests/setup.ts'],

  // Cobertura de código
  collectCoverage: true,
  collectCoverageFrom: [
    'src/lib/**/*.ts',
    'src/app/api/**/*.ts',
    '!src/lib/**/*.d.ts',
    '!src/tests/**/*',
    '!**/node_modules/**'
  ],

  // Diretório de saída da cobertura
  coverageDirectory: 'coverage',

  // Formatos de relatório de cobertura
  coverageReporters: [
    'text',
    'text-summary',
    'html',
    'lcov'
  ],

  // Limites de cobertura
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    }
  },

  // Timeout para testes
  testTimeout: 10000,

  // Configurações específicas para diferentes tipos de teste
  projects: [
    {
      displayName: 'Unit Tests',
      testMatch: ['<rootDir>/src/tests/**/*.test.ts'],
      testEnvironment: 'node'
    }
  ],

  // Configuração de mocks
  clearMocks: true,
  resetMocks: true,
  restoreMocks: true,

  // Configuração de verbose
  verbose: true,

  // Configuração de cache
  cache: true,
  cacheDirectory: '<rootDir>/.jest-cache',

  // Configuração de workers
  maxWorkers: '50%',

  // Configuração de notificações
  notify: false,

  // Configuração de bail (parar na primeira falha)
  bail: false,

  // Configuração de detectOpenHandles
  detectOpenHandles: true,

  // Configuração de forceExit
  forceExit: false,

  // Configuração de silent
  silent: false,

  // Configuração de errorOnDeprecated
  errorOnDeprecated: true,

  // Configuração de globals
  globals: {
    'ts-jest': {
      tsconfig: {
        compilerOptions: {
          module: 'commonjs',
          target: 'es2020',
          lib: ['es2020'],
          allowJs: true,
          skipLibCheck: true,
          strict: true,
          forceConsistentCasingInFileNames: true,
          noEmit: true,
          esModuleInterop: true,
          moduleResolution: 'node',
          resolveJsonModule: true,
          isolatedModules: true,
          incremental: true,
          baseUrl: '.',
          paths: {
            '@/*': ['./src/*']
          }
        }
      }
    }
  }
};
