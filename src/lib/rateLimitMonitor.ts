/**
 * Sistema de Monitorização e Gestão de Rate Limits da OpenAI
 * Implementa as melhores práticas para evitar erros 429
 */

interface RateLimitInfo {
  requestsRemaining: number;
  requestsLimit: number;
  tokensRemaining: number;
  tokensLimit: number;
  resetTime: Date;
}

interface RequestMetrics {
  timestamp: number;
  tokens: number;
  success: boolean;
}

/**
 * Monitor de rate limits com gestão proativa
 */
export class RateLimitMonitor {
  private requestHistory: RequestMetrics[] = [];
  private currentLimits: RateLimitInfo | null = null;
  private readonly windowSize = 60000; // 1 minuto em ms

  /**
   * Atualiza informações de rate limit a partir dos headers da resposta
   */
  updateFromHeaders(headers: Headers): void {
    const requestsRemaining = parseInt(headers.get('x-ratelimit-remaining-requests') || '0');
    const requestsLimit = parseInt(headers.get('x-ratelimit-limit-requests') || '0');
    const tokensRemaining = parseInt(headers.get('x-ratelimit-remaining-tokens') || '0');
    const tokensLimit = parseInt(headers.get('x-ratelimit-limit-tokens') || '0');
    const resetTime = new Date(headers.get('x-ratelimit-reset-requests') || Date.now());

    this.currentLimits = {
      requestsRemaining,
      requestsLimit,
      tokensRemaining,
      tokensLimit,
      resetTime
    };

    console.log(`📊 Rate Limits: ${requestsRemaining}/${requestsLimit} requests, ${tokensRemaining}/${tokensLimit} tokens`);
  }

  /**
   * Registra uma requisição no histórico
   */
  recordRequest(tokens: number, success: boolean): void {
    const now = Date.now();
    this.requestHistory.push({
      timestamp: now,
      tokens,
      success
    });

    // Limpar histórico antigo
    this.requestHistory = this.requestHistory.filter(
      record => now - record.timestamp < this.windowSize
    );
  }

  /**
   * Calcula delay recomendado antes da próxima requisição
   */
  getRecommendedDelay(): number {
    if (!this.currentLimits) return 0;

    const now = Date.now();
    const recentRequests = this.requestHistory.filter(
      record => now - record.timestamp < this.windowSize
    );

    // Se estamos próximos do limite de requests
    if (this.currentLimits.requestsRemaining < 5) {
      const timeUntilReset = this.currentLimits.resetTime.getTime() - now;
      return Math.max(0, timeUntilReset / this.currentLimits.requestsRemaining);
    }

    // Se estamos próximos do limite de tokens
    if (this.currentLimits.tokensRemaining < 1000) {
      return 2000; // 2 segundos de delay
    }

    // Se tivemos muitas requisições recentes
    if (recentRequests.length > 10) {
      return 1000; // 1 segundo de delay
    }

    return 0;
  }

  /**
   * Verifica se é seguro fazer uma requisição
   */
  canMakeRequest(estimatedTokens: number = 500): boolean {
    if (!this.currentLimits) return true;

    return (
      this.currentLimits.requestsRemaining > 1 &&
      this.currentLimits.tokensRemaining > estimatedTokens
    );
  }

  /**
   * Obtém estatísticas atuais
   */
  getStats(): {
    requestsInLastMinute: number;
    tokensInLastMinute: number;
    successRate: number;
    currentLimits: RateLimitInfo | null;
  } {
    const now = Date.now();
    const recentRequests = this.requestHistory.filter(
      record => now - record.timestamp < this.windowSize
    );

    const successfulRequests = recentRequests.filter(r => r.success);
    const successRate = recentRequests.length > 0 
      ? successfulRequests.length / recentRequests.length 
      : 1;

    return {
      requestsInLastMinute: recentRequests.length,
      tokensInLastMinute: recentRequests.reduce((sum, r) => sum + r.tokens, 0),
      successRate,
      currentLimits: this.currentLimits
    };
  }
}

// Instância global do monitor
export const rateLimitMonitor = new RateLimitMonitor();

/**
 * Função utilitária para adicionar delay inteligente
 */
export async function intelligentDelay(): Promise<void> {
  const delay = rateLimitMonitor.getRecommendedDelay();
  if (delay > 0) {
    console.log(`⏱️ Aguardando ${delay}ms para respeitar rate limits...`);
    await new Promise(resolve => setTimeout(resolve, delay));
  }
}

/**
 * Wrapper para requisições com monitorização automática
 */
export async function monitoredApiCall<T>(
  apiCall: () => Promise<T>,
  estimatedTokens: number = 500
): Promise<T> {
  // Verificar se podemos fazer a requisição
  if (!rateLimitMonitor.canMakeRequest(estimatedTokens)) {
    throw new Error('Rate limit preventivo ativado. Tente novamente em alguns segundos.');
  }

  // Aplicar delay inteligente
  await intelligentDelay();

  const startTime = Date.now();
  let success = false;
  
  try {
    const result = await apiCall();
    success = true;
    return result;
  } finally {
    // Registrar a requisição
    rateLimitMonitor.recordRequest(estimatedTokens, success);
  }
}
