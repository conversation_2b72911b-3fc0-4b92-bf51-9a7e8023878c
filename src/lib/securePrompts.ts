/**
 * Secure Prompt Management System
 * Uses OpenAI Prompt IDs instead of hardcoded prompts for better security
 */

import { getSecureConfig, ensureServerSide } from './secureConfig';
import { type ProductFormData } from './prompts';

/**
 * Prompt configuration using secure prompt IDs
 */
interface PromptConfig {
  promptId: string;
  variables?: Record<string, any>;
}

/**
 * Get prompt configuration for content generation
 */
export function getContentGenerationPrompt(productData: ProductFormData): PromptConfig {
  ensureServerSide('Content generation prompt access');
  
  const config = getSecureConfig();
  
  if (!config.openai.promptIds.contentGeneration) {
    throw new Error('🚨 SECURITY ERROR: Content generation prompt ID not configured');
  }

  // Prepare variables for the prompt
  const variables = {
    product_name: productData.name || '',
    product_category: productData.category || '',
    product_features: (productData.features || []).filter(f => f.trim()).join(', '),
    product_keywords: (productData.keywords || []).filter(k => k.trim()).join(', '),
    target_audience: productData.targetAudience || '',
    additional_info: productData.additionalInfo || '',
  };

  return {
    promptId: config.openai.promptIds.contentGeneration,
    variables
  };
}

/**
 * Get prompt configuration for content improvement
 */
export function getContentImprovementPrompt(currentDescription: string, productName?: string): PromptConfig {
  ensureServerSide('Content improvement prompt access');
  
  const config = getSecureConfig();
  
  if (!config.openai.promptIds.contentImprovement) {
    throw new Error('🚨 SECURITY ERROR: Content improvement prompt ID not configured');
  }

  const variables = {
    current_description: currentDescription,
    product_name: productName || 'Não fornecido',
  };

  return {
    promptId: config.openai.promptIds.contentImprovement,
    variables
  };
}

/**
 * Get prompt configuration for keyword suggestions
 */
export function getKeywordSuggestionPrompt(productName: string, productCategory?: string): PromptConfig {
  ensureServerSide('Keyword suggestion prompt access');
  
  const config = getSecureConfig();
  
  if (!config.openai.promptIds.keywordSuggestion) {
    throw new Error('🚨 SECURITY ERROR: Keyword suggestion prompt ID not configured');
  }

  const variables = {
    product_name: productName,
    product_category: productCategory || '',
  };

  return {
    promptId: config.openai.promptIds.keywordSuggestion,
    variables
  };
}

/**
 * Fallback prompts for when prompt IDs are not available (development/mock mode)
 * These are simplified versions for backward compatibility
 */
export const FALLBACK_PROMPTS = {
  contentGeneration: `
Gera para cada produto uma descrição SEO otimizada, uma descrição WooCommerce detalhada, e uma descrição curta, SEMPRE após um raciocínio detalhado sobre palavras-chave principais, benefícios e estratégia de adaptação para cada canal. Todos os textos devem ser produzidos em português de Portugal, respeitando os parâmetros obrigatórios e a estrutura abaixo.

- Sempre que receber um produto, faz primeiro o raciocínio detalhado:
    - Lista as palavras-chave principais do produto.
    - Enumera os principais benefícios e diferenciais a destacar para o público-alvo.
    - Explica como vais adaptar o foco e a linguagem para cada tipo de descrição: SEO, WooCommerce, e curta.

- Só após o raciocínio, gera as três descrições distintas:
    1. **Descrição SEO**: Uma frase detalhada, apelativa e otimizada para motores de busca, com EXATAMENTE 140-160 caracteres (obrigatório) e incluindo palavras-chave.
    2. **Descrição WooCommerce**: Texto objectivo, claro e comercial, entre 50 e 100 palavras, que realça principais benefícios e características, adequado para vitrines e páginas de produto da loja WooCommerce.
    3. **Descrição Curta**: Resumo ultracurto e impactante (máximo de 30 palavras), atraente e persuasivo para montras ou listagens, incentivando o clique.

- Se faltar informação específica sobre o produto, usa descrições genéricas apelativas baseadas no tipo de produto.
- Mantém sempre a ordem: raciocínio > descrições. Nunca começa pelas descrições.
- Todos os textos e raciocínios devem estar em português de Portugal e ser adequados ao comércio online WooCommerce.

Dados do produto:
- Nome: {{product_name}}
- Categoria: {{product_category}}
- Características: {{product_features}}
- Palavras-chave: {{product_keywords}}
- Público-alvo: {{target_audience}}
- Info adicional: {{additional_info}}

Responde em JSON seguindo EXATAMENTE esta estrutura:
{
  "reasoning": {
    "keywords": "palavras-chave principais identificadas",
    "benefits": "benefícios e diferenciais a enfatizar",
    "strategy": "estratégia de adaptação para cada canal"
  },
  "wooCommerceMainDescription": "Descrição WooCommerce entre 50-100 palavras",
  "wooCommerceShortDescription": "Descrição curta máximo 30 palavras",
  "shortDescription": "Descrição SEO até 160 caracteres",
  "slug": "url-amigavel-do-produto"
}
`,

  contentImprovement: `
És um editor profissional. Melhora esta descrição de produto:

Produto: {{product_name}}
Descrição atual: {{current_description}}

Corrige erros, melhora a estrutura e otimiza para SEO.
Responde em JSON com os mesmos campos da descrição original.
`,

  keywordSuggestion: `
És um especialista em SEO para e-commerce português.

Produto: {{product_name}}
Categoria: {{product_category}}

Sugere 3-5 palavras-chave de cauda longa em português de Portugal.
Responde em JSON: {"keywords": ["palavra1", "palavra2", ...]}
`
};

/**
 * Replace variables in fallback prompts
 */
export function replaceFallbackPromptVariables(template: string, variables: Record<string, any>): string {
  let result = template;
  
  Object.entries(variables).forEach(([key, value]) => {
    const placeholder = `{{${key}}}`;
    result = result.replace(new RegExp(placeholder, 'g'), String(value || ''));
  });
  
  return result;
}

/**
 * Check if prompt IDs are available
 */
export function hasPromptIds(): boolean {
  try {
    const config = getSecureConfig();
    return !!(
      config.openai.promptIds.contentGeneration &&
      config.openai.promptIds.contentImprovement &&
      config.openai.promptIds.keywordSuggestion
    );
  } catch {
    return false;
  }
}

/**
 * Get OpenAI client configuration with proper security
 */
export function getOpenAIClientConfig() {
  ensureServerSide('OpenAI client configuration access');
  
  const config = getSecureConfig();
  
  if (!config.openai.apiKey && !config.app.mockMode) {
    throw new Error('🚨 SECURITY ERROR: OpenAI API key not configured and mock mode disabled');
  }

  return {
    apiKey: config.openai.apiKey,
    organization: config.openai.organizationId,
    dangerouslyAllowBrowser: false, // Ensure client-side usage is blocked
  };
}

/**
 * Validate prompt ID format
 */
export function validatePromptId(promptId: string): boolean {
  // Updated pattern to support the new 48-character format
  const promptIdPattern = /^pmpt_[a-f0-9]{48}$/;
  return promptIdPattern.test(promptId);
}

/**
 * Sanitize prompt variables for logging
 */
export function sanitizePromptVariables(variables: Record<string, any>): Record<string, any> {
  const sanitized: Record<string, any> = {};
  
  Object.entries(variables).forEach(([key, value]) => {
    if (typeof value === 'string' && value.length > 100) {
      sanitized[key] = value.substring(0, 100) + '...[truncated]';
    } else {
      sanitized[key] = value;
    }
  });
  
  return sanitized;
}
