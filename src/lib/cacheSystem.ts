/**
 * Sistema de Cache Inteligente para Conteúdo SEO
 * Otimiza performance evitando regeneração de conteúdo similar
 */

import { type SeoContent, type ProductFormData } from './seoGenerator';

// Interface para entrada do cache
interface CacheEntry {
  content: SeoContent;
  timestamp: number;
  hits: number;
  lastAccessed: number;
  hash: string;
}

// Configuração do cache
interface CacheConfig {
  maxEntries: number;
  ttlMinutes: number;
  similarityThreshold: number;
  enableSimilarityMatching: boolean;
}

// Configuração padrão
const DEFAULT_CACHE_CONFIG: CacheConfig = {
  maxEntries: 100,
  ttlMinutes: 60, // 1 hora
  similarityThreshold: 0.8, // 80% de similaridade
  enableSimilarityMatching: true
};

/**
 * Sistema de cache inteligente com detecção de similaridade
 */
export class IntelligentCache {
  private cache = new Map<string, CacheEntry>();
  private config: CacheConfig;

  constructor(config: CacheConfig = DEFAULT_CACHE_CONFIG) {
    this.config = config;
    
    // Limpeza periódica do cache
    setInterval(() => this.cleanup(), 5 * 60 * 1000); // A cada 5 minutos
  }

  /**
   * Gera chave de cache baseada nos dados do produto
   */
  private generateCacheKey(productData: ProductFormData, action: 'generate' | 'improve' = 'generate'): string {
    const normalizedData = this.normalizeProductData(productData);
    const dataString = JSON.stringify({ ...normalizedData, action });
    return this.hashString(dataString);
  }

  /**
   * Normaliza dados do produto para comparação consistente
   */
  private normalizeProductData(data: ProductFormData): any {
    return {
      name: data.name?.toLowerCase().trim(),
      category: data.category?.toLowerCase().trim() || '',
      features: data.features?.map(f => f.toLowerCase().trim()).filter(f => f).sort() || [],
      keywords: data.keywords?.map(k => k.toLowerCase().trim()).filter(k => k).sort() || [],
      targetAudience: data.targetAudience?.toLowerCase().trim() || '',
      additionalInfo: data.additionalInfo?.toLowerCase().trim() || ''
    };
  }

  /**
   * Gera hash simples para string
   */
  private hashString(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36);
  }

  /**
   * Calcula similaridade entre dois produtos
   */
  private calculateSimilarity(data1: any, data2: any): number {
    let score = 0;
    let totalWeight = 0;

    // Nome do produto (peso 40%)
    const nameWeight = 0.4;
    if (data1.name && data2.name) {
      score += this.stringSimilarity(data1.name, data2.name) * nameWeight;
    }
    totalWeight += nameWeight;

    // Categoria (peso 20%)
    const categoryWeight = 0.2;
    if (data1.category && data2.category) {
      score += this.stringSimilarity(data1.category, data2.category) * categoryWeight;
    }
    totalWeight += categoryWeight;

    // Características (peso 25%)
    const featuresWeight = 0.25;
    if (data1.features && data2.features) {
      score += this.arraysSimilarity(data1.features, data2.features) * featuresWeight;
    }
    totalWeight += featuresWeight;

    // Público-alvo (peso 15%)
    const audienceWeight = 0.15;
    if (data1.targetAudience && data2.targetAudience) {
      score += this.stringSimilarity(data1.targetAudience, data2.targetAudience) * audienceWeight;
    }
    totalWeight += audienceWeight;

    return totalWeight > 0 ? score / totalWeight : 0;
  }

  /**
   * Calcula similaridade entre duas strings
   */
  private stringSimilarity(str1: string, str2: string): number {
    if (str1 === str2) return 1;
    if (!str1 || !str2) return 0;

    const longer = str1.length > str2.length ? str1 : str2;
    const shorter = str1.length > str2.length ? str2 : str1;

    if (longer.length === 0) return 1;

    const editDistance = this.levenshteinDistance(longer, shorter);
    return (longer.length - editDistance) / longer.length;
  }

  /**
   * Calcula distância de Levenshtein
   */
  private levenshteinDistance(str1: string, str2: string): number {
    const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));

    for (let i = 0; i <= str1.length; i++) matrix[0][i] = i;
    for (let j = 0; j <= str2.length; j++) matrix[j][0] = j;

    for (let j = 1; j <= str2.length; j++) {
      for (let i = 1; i <= str1.length; i++) {
        const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
        matrix[j][i] = Math.min(
          matrix[j][i - 1] + 1, // deletion
          matrix[j - 1][i] + 1, // insertion
          matrix[j - 1][i - 1] + indicator // substitution
        );
      }
    }

    return matrix[str2.length][str1.length];
  }

  /**
   * Calcula similaridade entre arrays
   */
  private arraysSimilarity(arr1: string[], arr2: string[]): number {
    if (arr1.length === 0 && arr2.length === 0) return 1;
    if (arr1.length === 0 || arr2.length === 0) return 0;

    const set1 = new Set(arr1);
    const set2 = new Set(arr2);
    const intersection = new Set([...set1].filter(x => set2.has(x)));
    const union = new Set([...set1, ...set2]);

    return intersection.size / union.size;
  }

  /**
   * Busca conteúdo no cache
   */
  get(productData: ProductFormData, action: 'generate' | 'improve' = 'generate'): SeoContent | null {
    const key = this.generateCacheKey(productData, action);
    const entry = this.cache.get(key);

    if (entry && this.isEntryValid(entry)) {
      // Atualizar estatísticas de acesso
      entry.hits++;
      entry.lastAccessed = Date.now();
      
      console.log(`✅ Cache hit para produto: ${productData.name} (${entry.hits} hits)`);
      return entry.content;
    }

    // Se não encontrou correspondência exata, tentar busca por similaridade
    if (this.config.enableSimilarityMatching) {
      const similarEntry = this.findSimilarEntry(productData, action);
      if (similarEntry) {
        console.log(`🔍 Cache hit por similaridade para produto: ${productData.name}`);
        return similarEntry.content;
      }
    }

    console.log(`❌ Cache miss para produto: ${productData.name}`);
    return null;
  }

  /**
   * Busca entrada similar no cache
   */
  private findSimilarEntry(productData: ProductFormData, action: 'generate' | 'improve'): CacheEntry | null {
    const normalizedData = this.normalizeProductData(productData);
    let bestMatch: CacheEntry | null = null;
    let bestSimilarity = 0;

    for (const [key, entry] of this.cache.entries()) {
      if (!this.isEntryValid(entry)) continue;

      // Extrair dados do produto da entrada do cache
      const cachedData = this.extractProductDataFromHash(key, action);
      if (!cachedData) continue;

      const similarity = this.calculateSimilarity(normalizedData, cachedData);
      
      if (similarity > this.config.similarityThreshold && similarity > bestSimilarity) {
        bestSimilarity = similarity;
        bestMatch = entry;
      }
    }

    if (bestMatch) {
      bestMatch.hits++;
      bestMatch.lastAccessed = Date.now();
    }

    return bestMatch;
  }

  /**
   * Extrai dados do produto do hash (simplificado)
   */
  private extractProductDataFromHash(key: string, action: 'generate' | 'improve'): any | null {
    // Esta é uma implementação simplificada
    // Em produção, seria melhor armazenar os dados normalizados junto com a entrada
    return null;
  }

  /**
   * Armazena conteúdo no cache
   */
  set(productData: ProductFormData, content: SeoContent, action: 'generate' | 'improve' = 'generate'): void {
    const key = this.generateCacheKey(productData, action);
    const now = Date.now();

    const entry: CacheEntry = {
      content,
      timestamp: now,
      hits: 1,
      lastAccessed: now,
      hash: key
    };

    this.cache.set(key, entry);

    // Verificar se precisa limpar o cache
    if (this.cache.size > this.config.maxEntries) {
      this.evictOldEntries();
    }

    console.log(`💾 Conteúdo armazenado no cache para produto: ${productData.name}`);
  }

  /**
   * Verifica se entrada do cache ainda é válida
   */
  private isEntryValid(entry: CacheEntry): boolean {
    const now = Date.now();
    const ttlMs = this.config.ttlMinutes * 60 * 1000;
    return (now - entry.timestamp) < ttlMs;
  }

  /**
   * Remove entradas antigas do cache
   */
  private evictOldEntries(): void {
    const entries = Array.from(this.cache.entries());
    
    // Ordenar por última utilização (LRU)
    entries.sort(([, a], [, b]) => a.lastAccessed - b.lastAccessed);
    
    // Remover 20% das entradas mais antigas
    const toRemove = Math.floor(entries.length * 0.2);
    for (let i = 0; i < toRemove; i++) {
      this.cache.delete(entries[i][0]);
    }

    console.log(`🧹 Removidas ${toRemove} entradas antigas do cache`);
  }

  /**
   * Limpeza periódica do cache
   */
  private cleanup(): void {
    const initialSize = this.cache.size;
    const now = Date.now();
    const ttlMs = this.config.ttlMinutes * 60 * 1000;

    for (const [key, entry] of this.cache.entries()) {
      if ((now - entry.timestamp) > ttlMs) {
        this.cache.delete(key);
      }
    }

    const removedCount = initialSize - this.cache.size;
    if (removedCount > 0) {
      console.log(`🧹 Limpeza automática: removidas ${removedCount} entradas expiradas do cache`);
    }
  }

  /**
   * Estatísticas do cache
   */
  getStats(): {
    size: number;
    maxSize: number;
    hitRate: number;
    totalHits: number;
    oldestEntry: number;
    newestEntry: number;
  } {
    const entries = Array.from(this.cache.values());
    const totalHits = entries.reduce((sum, entry) => sum + entry.hits, 0);
    const totalRequests = entries.length > 0 ? totalHits : 1;
    
    const timestamps = entries.map(e => e.timestamp);
    
    return {
      size: this.cache.size,
      maxSize: this.config.maxEntries,
      hitRate: totalHits / totalRequests,
      totalHits,
      oldestEntry: timestamps.length > 0 ? Math.min(...timestamps) : 0,
      newestEntry: timestamps.length > 0 ? Math.max(...timestamps) : 0
    };
  }

  /**
   * Limpa todo o cache
   */
  clear(): void {
    this.cache.clear();
    console.log('🧹 Cache completamente limpo');
  }
}

// Instância global do cache
export const seoContentCache = new IntelligentCache();
