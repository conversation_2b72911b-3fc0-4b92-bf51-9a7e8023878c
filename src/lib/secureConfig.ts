/**
 * Secure Configuration Management System
 * Handles all sensitive data and environment variables securely
 * Ensures no credentials are exposed in client-side code
 */

// Environment variable validation schema
interface SecureConfig {
  openai: {
    apiKey: string;
    fallbackApiKey?: string;
    promptIds: {
      contentGeneration: string;
      contentImprovement: string;
      keywordSuggestion: string;
    };
    organizationId?: string;
  };
  app: {
    mockMode: boolean;
    environment: 'development' | 'production' | 'test';
    logLevel: 'error' | 'warn' | 'info' | 'debug';
  };
  security: {
    enableRateLimiting: boolean;
    maxRequestsPerMinute: number;
    enableInputSanitization: boolean;
  };
}

/**
 * Validates and loads secure configuration from environment variables
 * This function should ONLY be called on the server side
 */
function loadSecureConfig(): SecureConfig {
  // Ensure this only runs on server side
  if (typeof window !== 'undefined') {
    throw new Error('🚨 SECURITY ERROR: Secure config cannot be accessed on client side');
  }

  // Validate required environment variables
  const requiredVars = {
    OPENAI_API_KEY: process.env.OPENAI_API_KEY,
    OPENAI_PROMPT_ID_CONTENT_GENERATION: process.env.OPENAI_PROMPT_ID_CONTENT_GENERATION,
    OPENAI_PROMPT_ID_CONTENT_IMPROVEMENT: process.env.OPENAI_PROMPT_ID_CONTENT_IMPROVEMENT,
    OPENAI_PROMPT_ID_KEYWORD_SUGGESTION: process.env.OPENAI_PROMPT_ID_KEYWORD_SUGGESTION,
  };

  // Check for missing required variables
  const missingVars = Object.entries(requiredVars)
    .filter(([_, value]) => !value)
    .map(([key]) => key);

  if (missingVars.length > 0 && process.env.MOCK_OPENAI !== 'true') {
    throw new Error(
      `🚨 SECURITY ERROR: Missing required environment variables: ${missingVars.join(', ')}\n` +
      'Please check your .env.local file or enable MOCK_OPENAI=true for development.'
    );
  }

  // Validate API key format (basic validation)
  if (requiredVars.OPENAI_API_KEY && !requiredVars.OPENAI_API_KEY.startsWith('sk-')) {
    console.warn('⚠️ WARNING: OpenAI API key format appears invalid');
  }

  // Validate prompt ID format
  const promptIdPattern = /^pmpt_[a-f0-9]{32}$/;
  Object.entries(requiredVars).forEach(([key, value]) => {
    if (key.includes('PROMPT_ID') && value && !promptIdPattern.test(value)) {
      console.warn(`⚠️ WARNING: ${key} format appears invalid. Expected format: pmpt_[32 hex chars]`);
    }
  });

  return {
    openai: {
      apiKey: requiredVars.OPENAI_API_KEY || '',
      fallbackApiKey: process.env.OPENAI_FALLBACK_API_KEY,
      promptIds: {
        contentGeneration: requiredVars.OPENAI_PROMPT_ID_CONTENT_GENERATION || '',
        contentImprovement: requiredVars.OPENAI_PROMPT_ID_CONTENT_IMPROVEMENT || '',
        keywordSuggestion: requiredVars.OPENAI_PROMPT_ID_KEYWORD_SUGGESTION || '',
      },
      organizationId: process.env.OPENAI_ORGANIZATION_ID,
    },
    app: {
      mockMode: process.env.MOCK_OPENAI === 'true',
      environment: (process.env.NODE_ENV as any) || 'development',
      logLevel: (process.env.LOG_LEVEL as any) || 'info',
    },
    security: {
      enableRateLimiting: process.env.DISABLE_RATE_LIMITING !== 'true',
      maxRequestsPerMinute: parseInt(process.env.MAX_REQUESTS_PER_MINUTE || '60'),
      enableInputSanitization: process.env.DISABLE_INPUT_SANITIZATION !== 'true',
    },
  };
}

/**
 * Cached configuration instance
 */
let configCache: SecureConfig | null = null;

/**
 * Get secure configuration (server-side only)
 * Caches the configuration for performance
 */
export function getSecureConfig(): SecureConfig {
  if (!configCache) {
    configCache = loadSecureConfig();
    
    // Log configuration status (without sensitive data)
    console.log('🔒 Secure configuration loaded:', {
      mockMode: configCache.app.mockMode,
      environment: configCache.app.environment,
      hasApiKey: !!configCache.openai.apiKey,
      hasFallbackKey: !!configCache.openai.fallbackApiKey,
      hasPromptIds: {
        contentGeneration: !!configCache.openai.promptIds.contentGeneration,
        contentImprovement: !!configCache.openai.promptIds.contentImprovement,
        keywordSuggestion: !!configCache.openai.promptIds.keywordSuggestion,
      },
      rateLimitingEnabled: configCache.security.enableRateLimiting,
    });
  }
  
  return configCache;
}

/**
 * Get public configuration safe for client-side use
 * Only includes non-sensitive configuration
 */
export function getPublicConfig() {
  // This can be safely used on client side
  return {
    mockMode: process.env.NEXT_PUBLIC_MOCK_MODE === 'true',
    environment: process.env.NODE_ENV || 'development',
    version: process.env.NEXT_PUBLIC_APP_VERSION || '1.0.0',
  };
}

/**
 * Validate that we're running on server side for sensitive operations
 */
export function ensureServerSide(operation: string): void {
  if (typeof window !== 'undefined') {
    throw new Error(`🚨 SECURITY ERROR: ${operation} can only be performed on server side`);
  }
}

/**
 * Sanitize configuration for logging (removes sensitive data)
 */
export function sanitizeConfigForLogging(config: Partial<SecureConfig>): any {
  return {
    ...config,
    openai: {
      ...config.openai,
      apiKey: config.openai?.apiKey ? '[REDACTED]' : undefined,
      fallbackApiKey: config.openai?.fallbackApiKey ? '[REDACTED]' : undefined,
      promptIds: config.openai?.promptIds ? {
        contentGeneration: config.openai.promptIds.contentGeneration ? '[REDACTED]' : undefined,
        contentImprovement: config.openai.promptIds.contentImprovement ? '[REDACTED]' : undefined,
        keywordSuggestion: config.openai.promptIds.keywordSuggestion ? '[REDACTED]' : undefined,
      } : undefined,
    },
  };
}

/**
 * Reset configuration cache (useful for testing)
 */
export function resetConfigCache(): void {
  configCache = null;
}

// Export types for use in other modules
export type { SecureConfig };
