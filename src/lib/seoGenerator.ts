/**
 * Gerador de Conteúdo SEO Inteligente
 * Sistema avançado para validação, correção e geração de conteúdo
 */

import { ProductFormData } from './prompts';

// Interface para conteúdo SEO gerado
export interface SeoContent {
  shortDescription: string;
  slug: string;
  wooCommerceMainDescription: string;
  wooCommerceShortDescription: string;
  reasoning?: {
    keywords: string;
    benefits: string;
    strategy: string;
  };
}

// Interface para validação de limites SEO
export interface SeoValidation {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

/**
 * Validação avançada de limites SEO
 */
export function validateSeoLimits(content: SeoContent): SeoValidation {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Validar meta description (140-160 caracteres - obrigatório)
  if (content.shortDescription.length < 140) {
    errors.push(`Meta description muito curta: ${content.shortDescription.length} caracteres (mínimo: 140)`);
  } else if (content.shortDescription.length > 160) {
    errors.push(`Meta description muito longa: ${content.shortDescription.length} caracteres (máximo: 160)`);
  } else if (content.shortDescription.length < 150) {
    warnings.push(`Meta description poderia ser mais longa para melhor SEO: ${content.shortDescription.length} caracteres`);
  }

  // Validar slug SEO (recomendações)
  if (content.slug.length > 60) {
    warnings.push(`Slug longo demais: ${content.slug.length} caracteres (recomendado: máx. 60)`);
  }

  if (content.slug.includes('_')) {
    warnings.push('Slug contém underscores - hífens são preferíveis para SEO');
  }

  // Validar descrição principal
  if (content.wooCommerceMainDescription.length < 200) {
    warnings.push('Descrição principal muito curta - considere adicionar mais detalhes');
  }

  // Validar descrição curta WooCommerce
  if (content.wooCommerceShortDescription.length > 200) {
    warnings.push(`Descrição curta WooCommerce muito longa: ${content.wooCommerceShortDescription.length} caracteres`);
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * Geração inteligente de slug SEO-friendly
 */
export function generateSlug(text: string): string {
  if (!text) return '';

  // Mapeamento de caracteres especiais portugueses
  const charMap: { [key: string]: string } = {
    'à': 'a', 'á': 'a', 'â': 'a', 'ã': 'a', 'ä': 'a', 'å': 'a', 'ā': 'a', 'ă': 'a', 'ą': 'a',
    'è': 'e', 'é': 'e', 'ê': 'e', 'ë': 'e', 'ē': 'e', 'ė': 'e', 'ę': 'e', 'ě': 'e',
    'ì': 'i', 'í': 'i', 'î': 'i', 'ï': 'i', 'ī': 'i', 'į': 'i',
    'ò': 'o', 'ó': 'o', 'ô': 'o', 'õ': 'o', 'ö': 'o', 'ø': 'o', 'ō': 'o', 'ő': 'o',
    'ù': 'u', 'ú': 'u', 'û': 'u', 'ü': 'u', 'ū': 'u', 'ů': 'u', 'ű': 'u', 'ų': 'u',
    'ç': 'c', 'ć': 'c', 'č': 'c',
    'ñ': 'n', 'ń': 'n', 'ň': 'n',
    'ý': 'y', 'ÿ': 'y',
    'ž': 'z', 'ź': 'z', 'ż': 'z',
    'š': 's', 'ś': 's', 'ş': 's', 'ș': 's',
    'ř': 'r', 'ŕ': 'r',
    'ť': 't', 'ț': 't',
    'ď': 'd', 'đ': 'd',
    'ł': 'l',
    'ğ': 'g', 'ǵ': 'g',
    'ḧ': 'h',
    'ṕ': 'p',
    'ß': 'ss',
    'æ': 'ae',
    'œ': 'oe'
  };

  return text
    .toString()
    .toLowerCase()
    .trim()
    // Substituir caracteres especiais
    .replace(/[àáâãäåāăąèéêëēėęěìíîïīįòóôõöøōőùúûüūůűųçćčñńňýÿžźżšśşșřŕťțďđłğǵḧṕßæœ]/g, 
      char => charMap[char] || char)
    // Substituir espaços e caracteres especiais por hífens
    .replace(/[\s\W]+/g, '-')
    // Remover hífens múltiplos
    .replace(/-+/g, '-')
    // Remover hífens do início e fim
    .replace(/^-+|-+$/g, '')
    // Limitar comprimento
    .substring(0, 60);
}

/**
 * Detecção inteligente do género gramatical de produtos
 */
export function detectProductGender(productName: string): 'masculine' | 'feminine' | 'unknown' {
  if (!productName) return 'unknown';

  const name = productName.toLowerCase().trim();

  // Produtos femininos comuns (expandido)
  const feminineProducts = [
    // Vestuário feminino
    'camisola', 'blusa', 'saia', 'calça', 'camisa', 'jaqueta', 'jaqueta', 'camiseta',
    'sandália', 'sapatilha', 'bota', 'meia', 'cueca', 'calcinha',
    
    // Casa e decoração
    'mesa', 'cadeira', 'lâmpada', 'cortina', 'almofada', 'toalha', 'estante', 
    'prateleira', 'gaveta', 'cama', 'televisão', 'máquina', 'impressora',
    
    // Cozinha
    'panela', 'frigideira', 'chaleira', 'torradeira', 'batedeira', 'geladeira',
    'cafeteira', 'liquidificadora', 'fritadeira',
    
    // Acessórios
    'bolsa', 'carteira', 'mochila', 'pulseira', 'corrente', 'aliança', 'pérola', 
    'gargantilha', 'escova', 'touca', 'luva',
    
    // Tecnologia
    'televisão', 'impressora', 'webcam', 'câmara', 'bateria', 'antena',
    
    // Outros
    'bicicleta', 'motocicleta', 'prancha', 'raquete', 'bola'
  ];

  // Produtos masculinos comuns (expandido)
  const masculineProducts = [
    // Vestuário masculino
    'casaco', 'sapato', 'tênis', 'chinelo', 'cinto', 'chapéu', 'boné',
    
    // Tecnologia
    'computador', 'telemóvel', 'tablet', 'smartphone', 'monitor', 'teclado', 
    'rato', 'auricular', 'auscultador', 'carregador', 'cabo', 'adaptador',
    'televisor', 'projetor', 'altifalante',
    
    // Casa
    'sofá', 'armário', 'frigorífico', 'micro-ondas', 'aspirador', 'secador', 
    'ferro', 'ventilador', 'aquecedor',
    
    // Acessórios
    'relógio', 'anel', 'colar', 'brinco', 'suporte', 'tripé', 'filtro',
    
    // Automóvel
    'carro', 'automóvel', 'veículo', 'pneu', 'volante', 'motor',
    
    // Desporto
    'equipamento', 'aparelho', 'dispositivo', 'instrumento'
  ];

  // Verificar correspondências exatas primeiro
  for (const feminine of feminineProducts) {
    if (name.includes(feminine)) return 'feminine';
  }

  for (const masculine of masculineProducts) {
    if (name.includes(masculine)) return 'masculine';
  }

  // Verificar terminações comuns
  if (name.match(/(a|ção|dade|gem|ice|ude)$/)) {
    return 'feminine';
  }

  if (name.match(/(o|or|ismo|ente|ante)$/)) {
    return 'masculine';
  }

  return 'unknown';
}

/**
 * Correção automática de ortografia portuguesa (expandida)
 */
export function correctSpelling(text: string): string {
  if (!text) return '';

  // Dicionário expandido de correções comuns
  const corrections: { [key: string]: string } = {
    // Erros ortográficos comuns
    'qualidadde': 'qualidade',
    'funcionalidadde': 'funcionalidade',
    'resistênte': 'resistente',
    'duravél': 'durável',
    'portátil': 'portátil',
    'ergonómico': 'ergonómico',
    'económico': 'económico',
    'tecnológico': 'tecnológico',
    'prático': 'prático',
    'automático': 'automático',
    'confortavél': 'confortável',
    'disponivél': 'disponível',
    'responsavél': 'responsável',
    'sustentavél': 'sustentável',
    'flexivél': 'flexível',
    'removivél': 'removível',

    // Consistência português europeu vs brasileiro
    'eletrônico': 'eletrónico',
    'eletrônicos': 'eletrónicos',
    'eletrônica': 'eletrónica',
    'electrónico': 'eletrónico',
    'electrónicos': 'eletrónicos',
    'electrónica': 'eletrónica',

    // Outras correções comuns
    'optimizar': 'otimizar',
    'optimizado': 'otimizado',
    'optimização': 'otimização',
    'característica': 'característica',
    'funcionalidade': 'funcionalidade',
    'acessório': 'acessório',
    'necessário': 'necessário',
    'extraordinário': 'extraordinário',
    'temporário': 'temporário',
    'primário': 'primário',
    'secundário': 'secundário',

    // Correções de acentuação
    'facil': 'fácil',
    'util': 'útil',
    'movel': 'móvel',
    'portatil': 'portátil',
    'pratico': 'prático',
    'automatico': 'automático',
    'economico': 'económico',
    'tecnologico': 'tecnológico',
    'ergonomico': 'ergonómico',

    // Correções de género comum
    'ajustável de alta qualidade': 'ajustável de alta qualidade',
    'resistente de alta qualidade': 'resistente de alta qualidade',
    'durável de alta qualidade': 'durável de alta qualidade'
  };

  let corrected = text;

  // Aplicar correções palavra por palavra
  Object.entries(corrections).forEach(([wrong, right]) => {
    const regex = new RegExp(`\\b${wrong}\\b`, 'gi');
    corrected = corrected.replace(regex, right);
  });

  // Correções específicas de frases problemáticas
  corrected = corrected.replace(/ajustável de alta qualidade/gi, 'de alta qualidade e ajustável');
  corrected = corrected.replace(/resistente de alta qualidade/gi, 'de alta qualidade e resistente');
  corrected = corrected.replace(/durável de alta qualidade/gi, 'de alta qualidade e durável');

  return corrected;
}

/**
 * Correção avançada de gramática e formatação (expandida)
 */
export function correctGrammarAndFormatting(text: string): string {
  if (!text) return '';

  let corrected = text;

  // Corrigir espaçamento
  corrected = corrected.replace(/\s+/g, ' '); // Múltiplos espaços para um
  corrected = corrected.replace(/\s+([.,;:!?])/g, '$1'); // Remover espaço antes de pontuação
  corrected = corrected.replace(/([.,;:!?])([a-zA-ZÀ-ÿ])/g, '$1 $2'); // Adicionar espaço após pontuação

  // Corrigir capitalização
  corrected = corrected.replace(/([.!?])\s+([a-zà-ÿ])/g, (_, punct, letter) => {
    return punct + ' ' + letter.toUpperCase();
  });

  // Garantir primeira letra maiúscula
  corrected = corrected.replace(/^\s*([a-zà-ÿ])/g, (_, letter) => {
    return letter.toUpperCase();
  });

  // Corrigir contrações portuguesas
  corrected = corrected.replace(/\bde a\b/g, 'da');
  corrected = corrected.replace(/\bde o\b/g, 'do');
  corrected = corrected.replace(/\bde os\b/g, 'dos');
  corrected = corrected.replace(/\bde as\b/g, 'das');
  corrected = corrected.replace(/\bem o\b/g, 'no');
  corrected = corrected.replace(/\bem a\b/g, 'na');
  corrected = corrected.replace(/\bem os\b/g, 'nos');
  corrected = corrected.replace(/\bem as\b/g, 'nas');
  corrected = corrected.replace(/\bpor o\b/g, 'pelo');
  corrected = corrected.replace(/\bpor a\b/g, 'pela');
  corrected = corrected.replace(/\bpor os\b/g, 'pelos');
  corrected = corrected.replace(/\bpor as\b/g, 'pelas');

  // Correções específicas de concordância de género
  corrected = correctGenderAgreement(corrected);

  // Normalizar formatação HTML se presente
  if (corrected.includes('<')) {
    corrected = corrected.replace(/>\s+</g, '><'); // Remover espaços entre tags
    corrected = corrected.replace(/\n\s*\n\s*\n/g, '\n\n'); // Máximo duas quebras de linha
    corrected = corrected.replace(/<p>\s*<\/p>/g, ''); // Remover parágrafos vazios
  }

  // Corrigir pontuação dupla
  corrected = corrected.replace(/[.,;:!?]{2,}/g, (match) => match[0]);

  // Corrigir espaços antes de dois pontos
  corrected = corrected.replace(/\s+:/g, ':');

  return corrected.trim();
}

/**
 * Correção específica de concordância de género
 */
function correctGenderAgreement(text: string): string {
  let corrected = text;

  // Correções específicas para produtos femininos
  const feminineCorrections: { [key: string]: string } = {
    'o camisola': 'a camisola',
    'este camisola': 'esta camisola',
    'um camisola': 'uma camisola',
    'o blusa': 'a blusa',
    'este blusa': 'esta blusa',
    'um blusa': 'uma blusa',
    'o mesa': 'a mesa',
    'este mesa': 'esta mesa',
    'um mesa': 'uma mesa',
    'o máquina': 'a máquina',
    'este máquina': 'esta máquina',
    'um máquina': 'uma máquina',
    'o televisão': 'a televisão',
    'este televisão': 'esta televisão',
    'um televisão': 'uma televisão',
    'desenvolvido especificamente': 'desenvolvida especificamente',
    'criado especificamente': 'criada especificamente',
    'projetado especificamente': 'projetada especificamente'
  };

  // Correções específicas para produtos masculinos
  const masculineCorrections: { [key: string]: string } = {
    'a computador': 'o computador',
    'esta computador': 'este computador',
    'uma computador': 'um computador',
    'a telemóvel': 'o telemóvel',
    'esta telemóvel': 'este telemóvel',
    'uma telemóvel': 'um telemóvel',
    'a sapato': 'o sapato',
    'esta sapato': 'este sapato',
    'uma sapato': 'um sapato',
    'desenvolvida especificamente': 'desenvolvido especificamente',
    'criada especificamente': 'criado especificamente',
    'projetada especificamente': 'projetado especificamente'
  };

  // Aplicar correções femininas
  Object.entries(feminineCorrections).forEach(([wrong, right]) => {
    const regex = new RegExp(`\\b${wrong}\\b`, 'gi');
    corrected = corrected.replace(regex, right);
  });

  // Aplicar correções masculinas
  Object.entries(masculineCorrections).forEach(([wrong, right]) => {
    const regex = new RegExp(`\\b${wrong}\\b`, 'gi');
    corrected = corrected.replace(regex, right);
  });

  return corrected;
}

/**
 * Padronização inteligente de títulos
 */
export function standardizeTitle(title: string): string {
  if (!title) return '';

  // Remover espaços extras e normalizar
  let standardized = title.trim().replace(/\s+/g, ' ');

  // Palavras que devem ficar em minúsculas (exceto no início)
  const lowercaseWords = ['de', 'da', 'do', 'das', 'dos', 'e', 'em', 'para', 'com', 'por', 'a', 'o', 'as', 'os'];

  standardized = standardized.toLowerCase().split(' ').map((word, index) => {
    // Sempre capitalizar primeira palavra
    if (index === 0) {
      return word.charAt(0).toUpperCase() + word.slice(1);
    }
    // Não capitalizar artigos/preposições a menos que sejam a primeira palavra
    if (lowercaseWords.includes(word)) {
      return word;
    }
    // Capitalizar outras palavras
    return word.charAt(0).toUpperCase() + word.slice(1);
  }).join(' ');

  // Remover pontuação desnecessária no final
  standardized = standardized.replace(/[.,;:!?]+$/, '');

  return standardized;
}

/**
 * Validação de dados do formulário (expandida)
 */
export function validateProductData(data: ProductFormData): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  // Nome é obrigatório
  if (!data.name || data.name.trim().length === 0) {
    errors.push('Nome do produto é obrigatório');
  } else if (data.name.trim().length < 3) {
    errors.push('Nome do produto deve ter pelo menos 3 caracteres');
  } else if (data.name.trim().length > 100) {
    errors.push('Nome do produto muito longo (máximo: 100 caracteres)');
  }

  // Validar categoria se fornecida
  if (data.category && data.category.trim().length > 50) {
    errors.push('Categoria muito longa (máximo: 50 caracteres)');
  }

  // Validar características se fornecidas
  if (data.features && data.features.length > 0) {
    const validFeatures = data.features.filter(f => f.trim().length > 0);
    if (validFeatures.length !== data.features.length) {
      errors.push('Algumas características estão vazias');
    }
    if (data.features.length > 10) {
      errors.push('Máximo de 10 características permitidas');
    }
    data.features.forEach((feature, index) => {
      if (feature.trim().length > 50) {
        errors.push(`Característica ${index + 1} muito longa (máximo: 50 caracteres)`);
      }
    });
  }

  // Validar palavras-chave se fornecidas
  if (data.keywords && data.keywords.length > 0) {
    const validKeywords = data.keywords.filter(k => k.trim().length > 0);
    if (validKeywords.length !== data.keywords.length) {
      errors.push('Algumas palavras-chave estão vazias');
    }
    if (data.keywords.length > 8) {
      errors.push('Máximo de 8 palavras-chave permitidas');
    }
    data.keywords.forEach((keyword, index) => {
      if (keyword.trim().length > 100) {
        errors.push(`Palavra-chave ${index + 1} muito longa (máximo: 100 caracteres)`);
      }
    });
  }

  // Validar público-alvo se fornecido
  if (data.targetAudience && data.targetAudience.trim().length > 100) {
    errors.push('Público-alvo muito longo (máximo: 100 caracteres)');
  }

  // Validar informações adicionais se fornecidas
  if (data.additionalInfo && data.additionalInfo.trim().length > 500) {
    errors.push('Informações adicionais muito longas (máximo: 500 caracteres)');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Validação robusta do output da API
 */
export function validateApiOutput(content: any): { isValid: boolean; errors: string[]; correctedContent?: SeoContent } {
  const errors: string[] = [];

  // Verificar se é um objeto válido
  if (!content || typeof content !== 'object') {
    errors.push('Resposta da API não é um objeto válido');
    return { isValid: false, errors };
  }

  // Verificar campos obrigatórios
  const requiredFields = ['wooCommerceMainDescription', 'wooCommerceShortDescription', 'shortDescription', 'slug'];
  const missingFields = requiredFields.filter(field => !content[field] || typeof content[field] !== 'string');

  if (missingFields.length > 0) {
    errors.push(`Campos obrigatórios em falta: ${missingFields.join(', ')}`);
    return { isValid: false, errors };
  }

  // Criar conteúdo corrigido
  const correctedContent: SeoContent = {
    wooCommerceMainDescription: correctGrammarAndFormatting(content.wooCommerceMainDescription),
    wooCommerceShortDescription: correctGrammarAndFormatting(content.wooCommerceShortDescription),
    shortDescription: correctGrammarAndFormatting(content.shortDescription),
    slug: generateSlug(content.slug)
  };

  // Adicionar reasoning se presente na resposta
  if (content.reasoning && typeof content.reasoning === 'object') {
    correctedContent.reasoning = {
      keywords: content.reasoning.keywords || '',
      benefits: content.reasoning.benefits || '',
      strategy: content.reasoning.strategy || ''
    };
  }

  // Validar comprimentos
  if (correctedContent.wooCommerceMainDescription.length < 100) {
    errors.push('Descrição principal muito curta (mínimo: 100 caracteres)');
  }

  if (correctedContent.wooCommerceShortDescription.length < 20) {
    errors.push('Descrição curta muito curta (mínimo: 20 caracteres)');
  }

  if (correctedContent.wooCommerceShortDescription.length > 200) {
    errors.push('Descrição curta muito longa (máximo: 200 caracteres)');
  }

  if (correctedContent.shortDescription.length < 140 || correctedContent.shortDescription.length > 160) {
    errors.push(`Meta description fora dos limites SEO: ${correctedContent.shortDescription.length} caracteres (deve ter 140-160)`);
  }

  if (!correctedContent.slug || correctedContent.slug.length < 3) {
    errors.push('Slug inválido ou muito curto');
  }

  // Verificar qualidade do conteúdo
  const qualityIssues = validateContentQuality(correctedContent);
  errors.push(...qualityIssues);

  return {
    isValid: errors.length === 0,
    errors,
    correctedContent
  };
}

/**
 * Validação da qualidade do conteúdo
 */
function validateContentQuality(content: SeoContent): string[] {
  const issues: string[] = [];

  // Verificar repetições excessivas
  const mainWords = content.wooCommerceMainDescription.toLowerCase().split(/\s+/);
  const wordCount: { [key: string]: number } = {};

  mainWords.forEach(word => {
    if (word.length > 4) { // Apenas palavras significativas
      wordCount[word] = (wordCount[word] || 0) + 1;
    }
  });

  const repeatedWords = Object.entries(wordCount).filter(([, count]) => count > 3);
  if (repeatedWords.length > 0) {
    issues.push(`Palavras repetidas excessivamente: ${repeatedWords.map(([word]) => word).join(', ')}`);
  }

  // Verificar se contém HTML válido na descrição principal
  if (content.wooCommerceMainDescription.includes('<') && !content.wooCommerceMainDescription.includes('<p>')) {
    issues.push('Descrição principal deve conter tags <p> para formatação adequada');
  }

  // Verificar se a descrição curta não contém HTML
  if (content.wooCommerceShortDescription.includes('<')) {
    issues.push('Descrição curta não deve conter tags HTML');
  }

  // Verificar se o slug é válido
  if (content.slug.includes(' ') || content.slug.includes('_') || /[A-Z]/.test(content.slug)) {
    issues.push('Slug deve usar apenas minúsculas e hífens');
  }

  return issues;
}
