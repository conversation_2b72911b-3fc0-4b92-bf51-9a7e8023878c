/**
 * Security Middleware for API Routes
 * Provides security checks and validation for all API endpoints
 */

import { NextRequest, NextResponse } from 'next/server';
import { validateServerSideSecurity, logSecurityValidation } from './securityValidator';
import { getSecureConfig } from './secureConfig';

/**
 * Security middleware configuration
 */
interface SecurityMiddlewareConfig {
  enableRateLimiting: boolean;
  enableInputValidation: boolean;
  enableSecurityHeaders: boolean;
  logSecurityEvents: boolean;
}

const DEFAULT_SECURITY_CONFIG: SecurityMiddlewareConfig = {
  enableRateLimiting: true,
  enableInputValidation: true,
  enableSecurityHeaders: true,
  logSecurityEvents: true,
};

/**
 * Rate limiting store (in-memory for simplicity)
 * In production, use Redis or similar persistent store
 */
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

/**
 * Security middleware for API routes
 */
export function withSecurity(
  handler: (req: NextRequest) => Promise<NextResponse>,
  config: Partial<SecurityMiddlewareConfig> = {}
) {
  const securityConfig = { ...DEFAULT_SECURITY_CONFIG, ...config };

  return async (req: NextRequest): Promise<NextResponse> => {
    try {
      // 1. Validate security configuration
      if (securityConfig.logSecurityEvents) {
        logSecurityValidation();
      }

      const securityValidation = validateServerSideSecurity();
      if (!securityValidation.isSecure) {
        console.error('🚨 Security validation failed:', securityValidation.errors);
        return NextResponse.json(
          { error: 'Security configuration error' },
          { status: 500 }
        );
      }

      // 2. Apply rate limiting
      if (securityConfig.enableRateLimiting) {
        const rateLimitResult = await applyRateLimit(req);
        if (!rateLimitResult.allowed) {
          return NextResponse.json(
            { 
              error: 'Rate limit exceeded. Please try again later.',
              retryAfter: rateLimitResult.retryAfter 
            },
            { 
              status: 429,
              headers: {
                'Retry-After': rateLimitResult.retryAfter.toString(),
                'X-RateLimit-Limit': rateLimitResult.limit.toString(),
                'X-RateLimit-Remaining': rateLimitResult.remaining.toString(),
                'X-RateLimit-Reset': rateLimitResult.resetTime.toString(),
              }
            }
          );
        }
      }

      // 3. Validate input
      if (securityConfig.enableInputValidation) {
        const inputValidation = await validateInput(req);
        if (!inputValidation.valid) {
          return NextResponse.json(
            { error: 'Invalid input', details: inputValidation.errors },
            { status: 400 }
          );
        }
      }

      // 4. Execute the handler
      const response = await handler(req);

      // 5. Apply security headers
      if (securityConfig.enableSecurityHeaders) {
        applySecurityHeaders(response);
      }

      return response;

    } catch (error) {
      console.error('🚨 Security middleware error:', error);
      return NextResponse.json(
        { error: 'Internal security error' },
        { status: 500 }
      );
    }
  };
}

/**
 * Apply rate limiting
 */
async function applyRateLimit(req: NextRequest): Promise<{
  allowed: boolean;
  limit: number;
  remaining: number;
  resetTime: number;
  retryAfter: number;
}> {
  const config = getSecureConfig();
  const limit = config.security.maxRequestsPerMinute;
  const windowMs = 60 * 1000; // 1 minute

  // Get client identifier (IP address)
  const clientId = getClientId(req);
  const now = Date.now();
  const windowStart = now - windowMs;

  // Get or create rate limit entry
  let entry = rateLimitStore.get(clientId);
  if (!entry || entry.resetTime < now) {
    entry = { count: 0, resetTime: now + windowMs };
    rateLimitStore.set(clientId, entry);
  }

  // Clean up old entries
  cleanupRateLimitStore(windowStart);

  // Check if limit exceeded
  if (entry.count >= limit) {
    return {
      allowed: false,
      limit,
      remaining: 0,
      resetTime: entry.resetTime,
      retryAfter: Math.ceil((entry.resetTime - now) / 1000),
    };
  }

  // Increment counter
  entry.count++;
  rateLimitStore.set(clientId, entry);

  return {
    allowed: true,
    limit,
    remaining: limit - entry.count,
    resetTime: entry.resetTime,
    retryAfter: 0,
  };
}

/**
 * Validate input data
 */
async function validateInput(req: NextRequest): Promise<{
  valid: boolean;
  errors: string[];
}> {
  const errors: string[] = [];

  try {
    // Check content type for POST requests
    if (req.method === 'POST') {
      const contentType = req.headers.get('content-type');
      if (!contentType || !contentType.includes('application/json')) {
        errors.push('Invalid content type. Expected application/json');
      }
    }

    // Check for suspicious patterns in URL
    const url = req.url;
    const suspiciousPatterns = [
      /<script/i,
      /javascript:/i,
      /on\w+=/i,
      /\.\./,
      /\/\.\./,
    ];

    if (suspiciousPatterns.some(pattern => pattern.test(url))) {
      errors.push('Suspicious URL pattern detected');
    }

    // Validate request size
    const contentLength = req.headers.get('content-length');
    if (contentLength && parseInt(contentLength) > 1024 * 1024) { // 1MB limit
      errors.push('Request too large');
    }

  } catch (error) {
    errors.push('Input validation error');
  }

  return {
    valid: errors.length === 0,
    errors,
  };
}

/**
 * Apply security headers to response
 */
function applySecurityHeaders(response: NextResponse): void {
  // Prevent XSS attacks
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-XSS-Protection', '1; mode=block');
  
  // Prevent information disclosure
  response.headers.set('Server', 'NextJS');
  response.headers.set('X-Powered-By', '');
  
  // Content Security Policy
  response.headers.set(
    'Content-Security-Policy',
    "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline';"
  );
  
  // Referrer Policy
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
}

/**
 * Get client identifier for rate limiting
 */
function getClientId(req: NextRequest): string {
  // Try to get real IP from various headers
  const forwarded = req.headers.get('x-forwarded-for');
  const realIp = req.headers.get('x-real-ip');
  const clientIp = req.headers.get('x-client-ip');
  
  const ip = forwarded?.split(',')[0] || realIp || clientIp || 'unknown';
  
  // Include user agent for better identification
  const userAgent = req.headers.get('user-agent') || 'unknown';
  
  return `${ip}:${userAgent.substring(0, 50)}`;
}

/**
 * Clean up old rate limit entries
 */
function cleanupRateLimitStore(cutoffTime: number): void {
  for (const [key, entry] of rateLimitStore.entries()) {
    if (entry.resetTime < cutoffTime) {
      rateLimitStore.delete(key);
    }
  }
}

/**
 * Security event logger
 */
export function logSecurityEvent(event: string, details: any = {}): void {
  const timestamp = new Date().toISOString();
  console.log(`🔒 [${timestamp}] Security Event: ${event}`, details);
}
