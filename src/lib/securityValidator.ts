/**
 * Security Validation Utilities
 * Ensures no sensitive data is exposed and validates security configurations
 */

import { getSecureConfig, getPublicConfig } from './secureConfig';

/**
 * Security check results
 */
interface SecurityCheckResult {
  isSecure: boolean;
  warnings: string[];
  errors: string[];
  recommendations: string[];
}

/**
 * Validate that sensitive data is not exposed in client-side code
 */
export function validateClientSideSecurity(): SecurityCheckResult {
  const result: SecurityCheckResult = {
    isSecure: true,
    warnings: [],
    errors: [],
    recommendations: []
  };

  // Check if we're running on client side
  if (typeof window !== 'undefined') {
    // These should NEVER be accessible on client side
    const sensitiveVars = [
      'OPENAI_API_KEY',
      'OPENAI_FALLBACK_API_KEY',
      'OPENAI_PROMPT_ID_CONTENT_GENERATION',
      'OPENAI_PROMPT_ID_CONTENT_IMPROVEMENT',
      'OPENAI_PROMPT_ID_KEYWORD_SUGGESTION',
      'OPENAI_ORGANIZATION_ID'
    ];

    sensitiveVars.forEach(varName => {
      // Check if variable is accessible (it shouldn't be)
      if ((process.env as any)[varName]) {
        result.isSecure = false;
        result.errors.push(`🚨 CRITICAL: ${varName} is exposed on client side!`);
      }
    });

    // Check for safe public variables
    const publicConfig = getPublicConfig();
    if (publicConfig.mockMode) {
      result.warnings.push('⚠️ Mock mode is enabled (development only)');
    }
  }

  return result;
}

/**
 * Validate server-side security configuration
 */
export function validateServerSideSecurity(): SecurityCheckResult {
  const result: SecurityCheckResult = {
    isSecure: true,
    warnings: [],
    errors: [],
    recommendations: []
  };

  // Ensure this only runs on server side
  if (typeof window !== 'undefined') {
    result.errors.push('🚨 Server-side security check called on client side');
    result.isSecure = false;
    return result;
  }

  try {
    const config = getSecureConfig();

    // Check API key configuration
    if (!config.openai.apiKey && !config.app.mockMode) {
      result.errors.push('🚨 No OpenAI API key configured and mock mode disabled');
      result.isSecure = false;
    }

    if (config.openai.apiKey && !config.openai.apiKey.startsWith('sk-')) {
      result.warnings.push('⚠️ OpenAI API key format appears invalid');
    }

    // Check prompt ID configuration
    const promptIds = config.openai.promptIds;
    const promptIdPattern = /^pmpt_[a-f0-9]{32}$/;

    if (!promptIds.contentGeneration) {
      result.warnings.push('⚠️ Content generation prompt ID not configured');
      result.recommendations.push('Set OPENAI_PROMPT_ID_CONTENT_GENERATION environment variable');
    } else if (!promptIdPattern.test(promptIds.contentGeneration)) {
      result.warnings.push('⚠️ Content generation prompt ID format appears invalid');
    }

    if (!promptIds.contentImprovement) {
      result.warnings.push('⚠️ Content improvement prompt ID not configured');
      result.recommendations.push('Set OPENAI_PROMPT_ID_CONTENT_IMPROVEMENT environment variable');
    } else if (!promptIdPattern.test(promptIds.contentImprovement)) {
      result.warnings.push('⚠️ Content improvement prompt ID format appears invalid');
    }

    if (!promptIds.keywordSuggestion) {
      result.warnings.push('⚠️ Keyword suggestion prompt ID not configured');
      result.recommendations.push('Set OPENAI_PROMPT_ID_KEYWORD_SUGGESTION environment variable');
    } else if (!promptIdPattern.test(promptIds.keywordSuggestion)) {
      result.warnings.push('⚠️ Keyword suggestion prompt ID format appears invalid');
    }

    // Check security settings
    if (!config.security.enableRateLimiting && config.app.environment === 'production') {
      result.warnings.push('⚠️ Rate limiting disabled in production environment');
      result.recommendations.push('Enable rate limiting for production');
    }

    if (!config.security.enableInputSanitization) {
      result.errors.push('🚨 Input sanitization disabled - security risk');
      result.isSecure = false;
    }

    // Check environment
    if (config.app.mockMode && config.app.environment === 'production') {
      result.errors.push('🚨 Mock mode enabled in production environment');
      result.isSecure = false;
    }

    // Check fallback configuration
    if (config.openai.fallbackApiKey && config.openai.fallbackApiKey === config.openai.apiKey) {
      result.warnings.push('⚠️ Fallback API key is the same as primary key');
      result.recommendations.push('Use different API keys for primary and fallback');
    }

  } catch (error) {
    result.errors.push(`🚨 Failed to validate security configuration: ${error}`);
    result.isSecure = false;
  }

  return result;
}

/**
 * Comprehensive security validation
 */
export function validateSecurity(): SecurityCheckResult {
  const clientResult = validateClientSideSecurity();
  const serverResult = typeof window === 'undefined' ? validateServerSideSecurity() : {
    isSecure: true,
    warnings: [],
    errors: [],
    recommendations: []
  };

  return {
    isSecure: clientResult.isSecure && serverResult.isSecure,
    warnings: [...clientResult.warnings, ...serverResult.warnings],
    errors: [...clientResult.errors, ...serverResult.errors],
    recommendations: [...clientResult.recommendations, ...serverResult.recommendations]
  };
}

/**
 * Log security validation results
 */
export function logSecurityValidation(): void {
  const result = validateSecurity();

  if (result.isSecure) {
    console.log('✅ Security validation passed');
  } else {
    console.error('❌ Security validation failed');
  }

  if (result.errors.length > 0) {
    console.error('🚨 Security Errors:');
    result.errors.forEach(error => console.error(`  ${error}`));
  }

  if (result.warnings.length > 0) {
    console.warn('⚠️ Security Warnings:');
    result.warnings.forEach(warning => console.warn(`  ${warning}`));
  }

  if (result.recommendations.length > 0) {
    console.info('💡 Security Recommendations:');
    result.recommendations.forEach(rec => console.info(`  ${rec}`));
  }
}

/**
 * Validate API key format
 */
export function validateApiKeyFormat(apiKey: string): boolean {
  // OpenAI API keys should start with 'sk-' and be at least 20 characters
  return apiKey.startsWith('sk-') && apiKey.length >= 20;
}

/**
 * Validate prompt ID format
 */
export function validatePromptIdFormat(promptId: string): boolean {
  const promptIdPattern = /^pmpt_[a-f0-9]{32}$/;
  return promptIdPattern.test(promptId);
}

/**
 * Check if running in secure environment
 */
export function isSecureEnvironment(): boolean {
  if (typeof window !== 'undefined') {
    // Client side - check for HTTPS in production
    return location.protocol === 'https:' || location.hostname === 'localhost';
  } else {
    // Server side - always considered secure
    return true;
  }
}

/**
 * Sanitize configuration for public display
 */
export function sanitizeConfigForDisplay(config: any): any {
  const sanitized = JSON.parse(JSON.stringify(config));
  
  // Recursively sanitize sensitive fields
  function sanitizeObject(obj: any): any {
    if (typeof obj !== 'object' || obj === null) return obj;
    
    for (const key in obj) {
      if (typeof obj[key] === 'string') {
        // Sanitize sensitive fields
        if (key.toLowerCase().includes('key') || 
            key.toLowerCase().includes('secret') || 
            key.toLowerCase().includes('token') ||
            key.toLowerCase().includes('password') ||
            key.toLowerCase().includes('prompt')) {
          obj[key] = '[REDACTED]';
        }
      } else if (typeof obj[key] === 'object') {
        sanitizeObject(obj[key]);
      }
    }
    
    return obj;
  }
  
  return sanitizeObject(sanitized);
}
