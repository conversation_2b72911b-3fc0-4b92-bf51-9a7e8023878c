/**
 * Testes para o sistema de fallbacks inteligentes
 */

import {
  generateIntelligentFallback,
  generateImprovementFallback
} from '../lib/fallbackGenerator';
import { validateSeoLimits, type ProductFormData } from '../lib/seoGenerator';

describe('Fallback Generator Tests', () => {
  
  describe('generateIntelligentFallback', () => {
    it('should generate valid content for electronics category', () => {
      const productData: ProductFormData = {
        name: 'Smartphone Samsung',
        category: 'eletrónica',
        features: ['resistente', 'rápido'],
        targetAudience: 'profissionais',
        additionalInfo: 'Com tecnologia 5G'
      };

      const result = generateIntelligentFallback(productData);

      // Verificar estrutura básica
      expect(result.wooCommerceMainDescription).toContain('<p>');
      expect(result.wooCommerceShortDescription).toContain('Smartphone Samsung');
      expect(result.shortDescription.length).toBeGreaterThanOrEqual(140);
      expect(result.shortDescription.length).toBeLessThanOrEqual(160);
      expect(result.slug).toBe('smartphone-samsung');

      // Verificar validação SEO
      const validation = validateSeoLimits(result);
      expect(validation.isValid).toBe(true);
    });

    it('should generate valid content for clothing category', () => {
      const productData: ProductFormData = {
        name: 'Camisola em Bico',
        category: 'vestuário',
        features: ['confortável', 'elegante'],
        targetAudience: 'mulheres modernas'
      };

      const result = generateIntelligentFallback(productData);

      // Verificar conteúdo específico para vestuário
      expect(result.wooCommerceMainDescription).toContain('vestuário');
      expect(result.wooCommerceShortDescription).toContain('Camisola em Bico');
      
      // Verificar validação SEO
      const validation = validateSeoLimits(result);
      expect(validation.isValid).toBe(true);
    });

    it('should handle minimal product data', () => {
      const productData: ProductFormData = {
        name: 'Produto Básico'
      };

      const result = generateIntelligentFallback(productData);

      // Deve gerar conteúdo mesmo com dados mínimos
      expect(result.wooCommerceMainDescription).toBeTruthy();
      expect(result.wooCommerceShortDescription).toBeTruthy();
      expect(result.shortDescription).toBeTruthy();
      expect(result.slug).toBe('produto-basico');

      // Verificar validação SEO
      const validation = validateSeoLimits(result);
      expect(validation.isValid).toBe(true);
    });

    it('should detect and use appropriate category template', () => {
      const categories = [
        { input: 'eletrónica', expected: 'tecnologia' },
        { input: 'vestuário', expected: 'estilo' },
        { input: 'casa', expected: 'ambiente' },
        { input: 'desporto', expected: 'performance' },
        { input: 'beleza', expected: 'cuidado' }
      ];

      categories.forEach(({ input, expected }) => {
        const productData: ProductFormData = {
          name: `Produto ${input}`,
          category: input
        };

        const result = generateIntelligentFallback(productData);
        
        // Verificar se o conteúdo contém palavras relacionadas à categoria
        const content = result.wooCommerceMainDescription.toLowerCase();
        expect(content).toContain(expected);
      });
    });

    it('should handle special characters in product names', () => {
      const productData: ProductFormData = {
        name: 'Açúcar Orgânico Português',
        category: 'alimentação'
      };

      const result = generateIntelligentFallback(productData);

      // Verificar se caracteres especiais são tratados corretamente
      expect(result.slug).toBe('acucar-organico-portugues');
      expect(result.wooCommerceShortDescription).toContain('Açúcar Orgânico Português');
    });

    it('should generate different content for same input (randomization)', () => {
      const productData: ProductFormData = {
        name: 'Produto Teste',
        category: 'casa'
      };

      const results = Array(5).fill(null).map(() => generateIntelligentFallback(productData));
      
      // Verificar que nem todos os resultados são idênticos (devido à randomização)
      const uniqueDescriptions = new Set(results.map(r => r.wooCommerceMainDescription));
      expect(uniqueDescriptions.size).toBeGreaterThan(1);
    });
  });

  describe('generateImprovementFallback', () => {
    it('should improve existing content', () => {
      const currentDescription = 'Este produto é bom e tem qualidade.';
      const productName = 'Produto Melhorado';

      const result = generateImprovementFallback(currentDescription, productName);

      // Verificar estrutura de melhoria
      expect(result.wooCommerceMainDescription).toContain('Versão Otimizada');
      expect(result.wooCommerceShortDescription).toContain('melhorado');
      expect(result.slug).toBe('produto-melhorado');

      // Verificar validação SEO
      const validation = validateSeoLimits(result);
      expect(validation.isValid).toBe(true);
    });

    it('should handle long descriptions', () => {
      const longDescription = 'Este é um produto muito bom com muitas características excelentes. '.repeat(10);
      const productName = 'Produto Complexo';

      const result = generateImprovementFallback(longDescription, productName);

      // Deve processar descrições longas sem problemas
      expect(result.wooCommerceMainDescription).toBeTruthy();
      expect(result.shortDescription.length).toBeLessThanOrEqual(160);
    });

    it('should work without product name', () => {
      const currentDescription = 'Descrição sem nome do produto.';

      const result = generateImprovementFallback(currentDescription);

      // Deve funcionar mesmo sem nome do produto
      expect(result.wooCommerceMainDescription).toBeTruthy();
      expect(result.slug).toBeTruthy();
    });

    it('should extract key features from description', () => {
      const currentDescription = 'Este produto resistente e durável oferece excelente qualidade e funcionalidade superior.';
      const productName = 'Produto Avançado';

      const result = generateImprovementFallback(currentDescription, productName);

      // Verificar se características foram extraídas e utilizadas
      const content = result.wooCommerceMainDescription.toLowerCase();
      expect(content).toContain('qualidade');
    });
  });

  describe('Content Quality Tests', () => {
    it('should generate grammatically correct Portuguese content', () => {
      const productData: ProductFormData = {
        name: 'Camisola Feminina',
        category: 'vestuário'
      };

      const result = generateIntelligentFallback(productData);

      // Verificar concordância de género
      expect(result.wooCommerceMainDescription).not.toContain('o camisola');
      expect(result.wooCommerceMainDescription).not.toContain('desenvolvido especificamente');
      
      // Deve usar concordância feminina
      expect(result.wooCommerceMainDescription).toMatch(/esta|a camisola/i);
    });

    it('should avoid repetitive content', () => {
      const productData: ProductFormData = {
        name: 'Produto Único',
        features: ['qualidade', 'qualidade', 'qualidade']
      };

      const result = generateIntelligentFallback(productData);

      // Contar ocorrências da palavra 'qualidade'
      const content = result.wooCommerceMainDescription.toLowerCase();
      const matches = content.match(/qualidade/g) || [];
      expect(matches.length).toBeLessThan(5); // Não deve repetir excessivamente
    });

    it('should generate appropriate length content', () => {
      const productData: ProductFormData = {
        name: 'Produto Teste'
      };

      const result = generateIntelligentFallback(productData);

      // Verificar comprimentos apropriados
      expect(result.wooCommerceMainDescription.length).toBeGreaterThan(200);
      expect(result.wooCommerceShortDescription.length).toBeLessThan(200);
      expect(result.shortDescription.length).toBeGreaterThanOrEqual(140);
      expect(result.shortDescription.length).toBeLessThanOrEqual(160);
    });

    it('should handle edge cases gracefully', () => {
      const edgeCases = [
        { name: '' },
        { name: 'A' },
        { name: 'Produto com nome muito muito muito muito muito muito longo que excede limites normais' },
        { name: 'Produto@#$%^&*()' }
      ];

      edgeCases.forEach(productData => {
        expect(() => {
          const result = generateIntelligentFallback(productData as ProductFormData);
          expect(result).toBeTruthy();
        }).not.toThrow();
      });
    });
  });

  describe('Performance Tests', () => {
    it('should generate content quickly', () => {
      const productData: ProductFormData = {
        name: 'Produto Performance',
        category: 'teste',
        features: ['rápido', 'eficiente'],
        targetAudience: 'utilizadores exigentes'
      };

      const startTime = Date.now();
      const result = generateIntelligentFallback(productData);
      const endTime = Date.now();

      // Deve gerar conteúdo em menos de 100ms
      expect(endTime - startTime).toBeLessThan(100);
      expect(result).toBeTruthy();
    });

    it('should handle multiple concurrent generations', () => {
      const productData: ProductFormData = {
        name: 'Produto Concorrente'
      };

      const promises = Array(10).fill(null).map(() => 
        Promise.resolve(generateIntelligentFallback(productData))
      );

      return Promise.all(promises).then(results => {
        expect(results).toHaveLength(10);
        results.forEach(result => {
          expect(result).toBeTruthy();
          expect(result.slug).toBe('produto-concorrente');
        });
      });
    });
  });
});
