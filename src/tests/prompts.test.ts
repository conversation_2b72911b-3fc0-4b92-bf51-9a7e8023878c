/**
 * Testes para o sistema de prompts melhora<PERSON>
 * Valida a capacidade de interpretação e reconstrução semântica
 */

import {
  SYSTEM_PROMPT_CONTENT_GENERATION,
  SYSTEM_PROMPT_CONTENT_IMPROVEMENT,
  generateUserPrompt,
  generateImprovementPrompt,
  type ProductFormData
} from '../lib/prompts';

describe('Sistema de Prompts Melhorado', () => {
  
  describe('System Prompts', () => {
    test('SYSTEM_PROMPT_CONTENT_GENERATION deve incluir instruções de reconstrução semântica', () => {
      expect(SYSTEM_PROMPT_CONTENT_GENERATION).toContain('dados informais e frequentemente mal estruturados');
      expect(SYSTEM_PROMPT_CONTENT_GENERATION).toContain('Interpretar corretamente a intenção e o significado');
      expect(SYSTEM_PROMPT_CONTENT_GENERATION).toContain('reconstrói tudo, se necessário');
      expect(SYSTEM_PROMPT_CONTENT_GENERATION).toContain('Evitar copiar literalmente os inputs');
      expect(SYSTEM_PROMPT_CONTENT_GENERATION).toContain('Interpretação e reconstrução semântica');
    });

    test('SYSTEM_PROMPT_CONTENT_IMPROVEMENT deve incluir instruções de reconstrução', () => {
      expect(SYSTEM_PROMPT_CONTENT_IMPROVEMENT).toContain('mal estruturadas');
      expect(SYSTEM_PROMPT_CONTENT_IMPROVEMENT).toContain('Interpretar corretamente a intenção');
      expect(SYSTEM_PROMPT_CONTENT_IMPROVEMENT).toContain('reconstrói um texto coerente');
      expect(SYSTEM_PROMPT_CONTENT_IMPROVEMENT).toContain('Nunca assumes que as palavras inseridas estão corretas');
    });
  });

  describe('generateUserPrompt', () => {
    test('deve gerar prompt com dados bem estruturados', () => {
      const productData: ProductFormData = {
        name: 'Smartphone Premium',
        category: 'Eletrónica',
        features: ['resistente', 'impermeável'],
        keywords: ['smartphone', 'premium'],
        targetAudience: 'profissionais',
        additionalInfo: 'Ideal para uso empresarial'
      };

      const prompt = generateUserPrompt(productData);
      
      expect(prompt).toContain('Nome do Produto: Smartphone Premium');
      expect(prompt).toContain('Categoria: Eletrónica');
      expect(prompt).toContain('Características Principais: resistente, impermeável');
      expect(prompt).toContain('Palavras-chave SEO: smartphone, premium');
      expect(prompt).toContain('Público-alvo: profissionais');
      expect(prompt).toContain('Informações Adicionais: Ideal para uso empresarial');
    });

    test('deve incluir instruções de reconstrução semântica', () => {
      const productData: ProductFormData = {
        name: 'Produto Teste'
      };

      const prompt = generateUserPrompt(productData);
      
      expect(prompt).toContain('Interpreta e reconstrói o conteúdo');
      expect(prompt).toContain('podem conter erros ortográficos, gramaticais');
      expect(prompt).toContain('Interpreta a intenção correta');
      expect(prompt).toContain('reconstrói tudo de forma profissional');
    });

    test('deve lidar com dados mal estruturados (simulação)', () => {
      // Simula dados como um utilizador real poderia inserir
      const productData: ProductFormData = {
        name: 'o camisola em bico',  // erro de género
        category: 'roupo inverno',   // erro ortográfico
        features: ['elastica', 'resistente'],  // sem acentos
        keywords: ['camisola', 'homen'],  // erro ortográfico
        targetAudience: 'homen',  // erro ortográfico
        additionalInfo: 'bom para frio. sem costura. design bom'  // informal
      };

      const prompt = generateUserPrompt(productData);
      
      // Verifica se os dados são incluídos (mesmo com erros)
      expect(prompt).toContain('o camisola em bico');
      expect(prompt).toContain('roupo inverno');
      expect(prompt).toContain('elastica, resistente');
      expect(prompt).toContain('homen');
      expect(prompt).toContain('bom para frio. sem costura. design bom');
      
      // Verifica se as instruções de correção estão presentes
      expect(prompt).toContain('Interpreta e reconstrói o conteúdo');
      expect(prompt).toContain('Corrige erros ortográficos, gramaticais e de concordância');
    });

    test('deve limpar dados vazios corretamente', () => {
      const productData: ProductFormData = {
        name: 'Produto Teste',
        category: '',
        features: ['', 'característica válida', ''],
        keywords: ['', 'palavra-chave', ''],
        targetAudience: '   ',
        additionalInfo: ''
      };

      const prompt = generateUserPrompt(productData);
      
      expect(prompt).toContain('Nome do Produto: Produto Teste');
      expect(prompt).not.toContain('Categoria:');
      expect(prompt).toContain('Características Principais: característica válida');
      expect(prompt).toContain('Palavras-chave SEO: palavra-chave');
      expect(prompt).not.toContain('Público-alvo:');
      expect(prompt).not.toContain('Informações Adicionais:');
    });
  });

  describe('generateImprovementPrompt', () => {
    test('deve gerar prompt de melhoria com instruções de reconstrução', () => {
      const currentDescription = 'O produto é bom e resistente para usar no trabalho.';
      const productName = 'Smartphone Premium';

      const prompt = generateImprovementPrompt(currentDescription, productName);
      
      expect(prompt).toContain('Nome do Produto: Smartphone Premium');
      expect(prompt).toContain('Descrição Atual: "O produto é bom e resistente para usar no trabalho."');
      expect(prompt).toContain('Interpreta e reconstrói o conteúdo');
      expect(prompt).toContain('pode conter erros ortográficos, gramaticais');
      expect(prompt).toContain('reconstrói tudo de forma profissional');
    });

    test('deve lidar com descrição mal estruturada', () => {
      const currentDescription = 'o camisola é muito bom para homen que gosta de qualidade e conforto no inverno frio';
      const productName = 'Camisola Premium';

      const prompt = generateImprovementPrompt(currentDescription, productName);
      
      expect(prompt).toContain(currentDescription);
      expect(prompt).toContain('Identifica e corrige erros de género gramatical');
      expect(prompt).toContain('o camisola" → ✅ "a camisola');
      expect(prompt).toContain('reconstrói se necessário');
    });

    test('deve funcionar sem nome do produto', () => {
      const currentDescription = 'Produto de qualidade superior.';

      const prompt = generateImprovementPrompt(currentDescription);
      
      expect(prompt).toContain('Nome do Produto: Não fornecido');
      expect(prompt).toContain('Descrição Atual: "Produto de qualidade superior."');
    });
  });

  describe('Validação de Estrutura', () => {
    test('prompts devem incluir formato JSON esperado', () => {
      const productData: ProductFormData = { name: 'Teste' };
      const userPrompt = generateUserPrompt(productData);
      const improvementPrompt = generateImprovementPrompt('Teste');

      // Verificar se ambos os prompts incluem a estrutura JSON esperada
      const expectedFields = [
        'wooCommerceMainDescription',
        'wooCommerceShortDescription', 
        'shortDescription',
        'slug'
      ];

      expectedFields.forEach(field => {
        expect(userPrompt).toContain(field);
        expect(improvementPrompt).toContain(field);
      });
    });

    test('prompts devem incluir regras de português de Portugal', () => {
      const productData: ProductFormData = { name: 'Teste' };
      const userPrompt = generateUserPrompt(productData);
      const improvementPrompt = generateImprovementPrompt('Teste');

      expect(userPrompt).toContain('português de Portugal');
      expect(userPrompt).toContain('Nunca uses português do Brasil');
      expect(improvementPrompt).toContain('português de Portugal');
      expect(improvementPrompt).toContain('Nunca uses português do Brasil');
    });
  });
});
