/**
 * Testes para o sistema de cache inteligente
 */

import { IntelligentCache } from '../lib/cacheSystem';
import { type ProductFormData, type SeoContent } from '../lib/seoGenerator';

describe('Intelligent Cache Tests', () => {
  let cache: IntelligentCache;
  
  const mockSeoContent: SeoContent = {
    wooCommerceMainDescription: '<p>Descrição principal de teste</p>',
    wooCommerceShortDescription: 'Descrição curta de teste',
    shortDescription: 'Meta description de teste com exatamente 150 caracteres para validar o sistema de cache e garantir que funciona corretamente.',
    slug: 'produto-teste'
  };

  const mockProductData: ProductFormData = {
    name: 'Produto Teste',
    category: 'eletrónica',
    features: ['resistente', 'durável'],
    keywords: ['teste', 'produto'],
    targetAudience: 'profissionais',
    additionalInfo: 'Informações adicionais'
  };

  beforeEach(() => {
    cache = new IntelligentCache({
      maxEntries: 10,
      ttlMinutes: 60,
      similarityThreshold: 0.8,
      enableSimilarityMatching: true
    });
  });

  describe('Basic Cache Operations', () => {
    it('should store and retrieve content', () => {
      // Armazenar conteúdo
      cache.set(mockProductData, mockSeoContent, 'generate');

      // Recuperar conteúdo
      const retrieved = cache.get(mockProductData, 'generate');
      
      expect(retrieved).toEqual(mockSeoContent);
    });

    it('should return null for non-existent content', () => {
      const nonExistentData: ProductFormData = {
        name: 'Produto Inexistente'
      };

      const retrieved = cache.get(nonExistentData, 'generate');
      expect(retrieved).toBeNull();
    });

    it('should handle different actions separately', () => {
      cache.set(mockProductData, mockSeoContent, 'generate');
      
      // Mesmo produto, ação diferente
      const retrieved = cache.get(mockProductData, 'improve');
      expect(retrieved).toBeNull();
    });

    it('should clear all cache entries', () => {
      cache.set(mockProductData, mockSeoContent, 'generate');
      
      const stats = cache.getStats();
      expect(stats.size).toBe(1);
      
      cache.clear();
      
      const statsAfterClear = cache.getStats();
      expect(statsAfterClear.size).toBe(0);
    });
  });

  describe('Cache Key Generation', () => {
    it('should generate same key for identical data', () => {
      const data1: ProductFormData = {
        name: 'Produto A',
        category: 'categoria',
        features: ['feature1', 'feature2']
      };

      const data2: ProductFormData = {
        name: 'Produto A',
        category: 'categoria',
        features: ['feature1', 'feature2']
      };

      cache.set(data1, mockSeoContent, 'generate');
      const retrieved = cache.get(data2, 'generate');
      
      expect(retrieved).toEqual(mockSeoContent);
    });

    it('should normalize data for consistent keys', () => {
      const data1: ProductFormData = {
        name: 'PRODUTO TESTE',
        features: ['Feature1', 'FEATURE2']
      };

      const data2: ProductFormData = {
        name: 'produto teste',
        features: ['feature1', 'feature2']
      };

      cache.set(data1, mockSeoContent, 'generate');
      const retrieved = cache.get(data2, 'generate');
      
      expect(retrieved).toEqual(mockSeoContent);
    });

    it('should handle empty and undefined fields', () => {
      const data1: ProductFormData = {
        name: 'Produto',
        category: '',
        features: [],
        keywords: undefined
      };

      const data2: ProductFormData = {
        name: 'Produto'
      };

      cache.set(data1, mockSeoContent, 'generate');
      const retrieved = cache.get(data2, 'generate');
      
      expect(retrieved).toEqual(mockSeoContent);
    });
  });

  describe('Similarity Matching', () => {
    it('should find similar products', () => {
      const originalData: ProductFormData = {
        name: 'Smartphone Samsung Galaxy',
        category: 'eletrónica',
        features: ['resistente', 'rápido']
      };

      const similarData: ProductFormData = {
        name: 'Smartphone Samsung',
        category: 'eletrónica',
        features: ['resistente', 'durável']
      };

      cache.set(originalData, mockSeoContent, 'generate');
      const retrieved = cache.get(similarData, 'generate');
      
      expect(retrieved).toEqual(mockSeoContent);
    });

    it('should not match dissimilar products', () => {
      const data1: ProductFormData = {
        name: 'Smartphone',
        category: 'eletrónica'
      };

      const data2: ProductFormData = {
        name: 'Camisola',
        category: 'vestuário'
      };

      cache.set(data1, mockSeoContent, 'generate');
      const retrieved = cache.get(data2, 'generate');
      
      expect(retrieved).toBeNull();
    });

    it('should respect similarity threshold', () => {
      const strictCache = new IntelligentCache({
        maxEntries: 10,
        ttlMinutes: 60,
        similarityThreshold: 0.95, // Muito restritivo
        enableSimilarityMatching: true
      });

      const data1: ProductFormData = {
        name: 'Produto Original'
      };

      const data2: ProductFormData = {
        name: 'Produto Diferente'
      };

      strictCache.set(data1, mockSeoContent, 'generate');
      const retrieved = strictCache.get(data2, 'generate');
      
      expect(retrieved).toBeNull();
    });
  });

  describe('Cache Expiration', () => {
    it('should expire old entries', (done) => {
      const shortTtlCache = new IntelligentCache({
        maxEntries: 10,
        ttlMinutes: 0.01, // 0.6 segundos
        similarityThreshold: 0.8,
        enableSimilarityMatching: true
      });

      shortTtlCache.set(mockProductData, mockSeoContent, 'generate');
      
      // Verificar que está no cache
      let retrieved = shortTtlCache.get(mockProductData, 'generate');
      expect(retrieved).toEqual(mockSeoContent);
      
      // Aguardar expiração
      setTimeout(() => {
        retrieved = shortTtlCache.get(mockProductData, 'generate');
        expect(retrieved).toBeNull();
        done();
      }, 1000);
    });
  });

  describe('Cache Eviction', () => {
    it('should evict old entries when max size is reached', () => {
      const smallCache = new IntelligentCache({
        maxEntries: 2,
        ttlMinutes: 60,
        similarityThreshold: 0.8,
        enableSimilarityMatching: true
      });

      // Adicionar 3 entradas (excede o limite)
      const data1: ProductFormData = { name: 'Produto 1' };
      const data2: ProductFormData = { name: 'Produto 2' };
      const data3: ProductFormData = { name: 'Produto 3' };

      smallCache.set(data1, mockSeoContent, 'generate');
      smallCache.set(data2, mockSeoContent, 'generate');
      smallCache.set(data3, mockSeoContent, 'generate');

      const stats = smallCache.getStats();
      expect(stats.size).toBeLessThanOrEqual(2);
    });
  });

  describe('Cache Statistics', () => {
    it('should track cache statistics', () => {
      cache.set(mockProductData, mockSeoContent, 'generate');
      
      // Fazer alguns hits
      cache.get(mockProductData, 'generate');
      cache.get(mockProductData, 'generate');
      
      const stats = cache.getStats();
      
      expect(stats.size).toBe(1);
      expect(stats.totalHits).toBeGreaterThan(0);
      expect(stats.hitRate).toBeGreaterThan(0);
    });

    it('should provide meaningful statistics', () => {
      // Adicionar várias entradas
      for (let i = 0; i < 5; i++) {
        const data: ProductFormData = { name: `Produto ${i}` };
        cache.set(data, mockSeoContent, 'generate');
      }

      const stats = cache.getStats();
      
      expect(stats.size).toBe(5);
      expect(stats.maxSize).toBe(10);
      expect(stats.oldestEntry).toBeGreaterThan(0);
      expect(stats.newestEntry).toBeGreaterThan(0);
    });
  });

  describe('Performance Tests', () => {
    it('should perform cache operations quickly', () => {
      const startTime = Date.now();
      
      // Realizar muitas operações
      for (let i = 0; i < 100; i++) {
        const data: ProductFormData = { name: `Produto ${i}` };
        cache.set(data, mockSeoContent, 'generate');
        cache.get(data, 'generate');
      }
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      // Deve completar em menos de 1 segundo
      expect(duration).toBeLessThan(1000);
    });

    it('should handle concurrent operations', async () => {
      const operations = Array(50).fill(null).map((_, i) => {
        const data: ProductFormData = { name: `Produto Concorrente ${i}` };
        return Promise.resolve().then(() => {
          cache.set(data, mockSeoContent, 'generate');
          return cache.get(data, 'generate');
        });
      });

      const results = await Promise.all(operations);
      
      expect(results).toHaveLength(50);
      results.forEach(result => {
        expect(result).toEqual(mockSeoContent);
      });
    });
  });

  describe('Edge Cases', () => {
    it('should handle malformed product data', () => {
      const malformedData = {
        name: null,
        category: undefined,
        features: 'not an array'
      } as any;

      expect(() => {
        cache.set(malformedData, mockSeoContent, 'generate');
        cache.get(malformedData, 'generate');
      }).not.toThrow();
    });

    it('should handle very long product names', () => {
      const longNameData: ProductFormData = {
        name: 'A'.repeat(1000)
      };

      cache.set(longNameData, mockSeoContent, 'generate');
      const retrieved = cache.get(longNameData, 'generate');
      
      expect(retrieved).toEqual(mockSeoContent);
    });

    it('should handle special characters in product data', () => {
      const specialData: ProductFormData = {
        name: 'Produto@#$%^&*()_+{}[]|\\:";\'<>?,./`~',
        category: 'Categoria çom açentos',
        features: ['Característíca especial', 'Outra característíca']
      };

      cache.set(specialData, mockSeoContent, 'generate');
      const retrieved = cache.get(specialData, 'generate');
      
      expect(retrieved).toEqual(mockSeoContent);
    });
  });
});
