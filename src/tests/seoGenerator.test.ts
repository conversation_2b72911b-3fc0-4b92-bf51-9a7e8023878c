/**
 * Testes para o sistema de geração de conteúdo SEO
 */

import {
  validateSeoLimits,
  generateSlug,
  detectProductGender,
  correctSpelling,
  correctGrammarAndFormatting,
  standardizeTitle,
  validateProductData,
  validateApiOutput,
  type SeoContent,
  type ProductFormData
} from '../lib/seoGenerator';

describe('SEO Generator Tests', () => {
  
  describe('validateSeoLimits', () => {
    it('should validate correct SEO limits', () => {
      const content: SeoContent = {
        shortDescription: 'Esta é uma meta description com exatamente 150 caracteres para testar a validação dos limites SEO estabelecidos para otimização.',
        slug: 'produto-teste',
        wooCommerceMainDescription: '<p>Descrição principal com mais de 100 caracteres para passar na validação.</p>',
        wooCommerceShortDescription: 'Descrição curta adequada'
      };

      const result = validateSeoLimits(content);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should detect meta description too short', () => {
      const content: SeoContent = {
        shortDescription: 'Muito curta',
        slug: 'produto-teste',
        wooCommerceMainDescription: '<p>Descrição principal.</p>',
        wooCommerceShortDescription: 'Descrição curta'
      };

      const result = validateSeoLimits(content);
      expect(result.isValid).toBe(false);
      expect(result.errors.some(e => e.includes('muito curta'))).toBe(true);
    });

    it('should detect meta description too long', () => {
      const content: SeoContent = {
        shortDescription: 'Esta é uma meta description extremamente longa que excede o limite de 160 caracteres estabelecido para otimização SEO e deve ser detectada como inválida pelo sistema.',
        slug: 'produto-teste',
        wooCommerceMainDescription: '<p>Descrição principal.</p>',
        wooCommerceShortDescription: 'Descrição curta'
      };

      const result = validateSeoLimits(content);
      expect(result.isValid).toBe(false);
      expect(result.errors.some(e => e.includes('muito longa'))).toBe(true);
    });
  });

  describe('generateSlug', () => {
    it('should generate valid slug from Portuguese text', () => {
      const result = generateSlug('Camisola em Bico Azul');
      expect(result).toBe('camisola-em-bico-azul');
    });

    it('should handle special Portuguese characters', () => {
      const result = generateSlug('Açúcar Orgânico Português');
      expect(result).toBe('acucar-organico-portugues');
    });

    it('should remove invalid characters', () => {
      const result = generateSlug('Produto@#$%^&*()_+{}[]|\\:";\'<>?,./');
      expect(result).toBe('produto');
    });

    it('should handle empty string', () => {
      const result = generateSlug('');
      expect(result).toBe('');
    });
  });

  describe('detectProductGender', () => {
    it('should detect feminine products', () => {
      expect(detectProductGender('Camisola Azul')).toBe('feminine');
      expect(detectProductGender('Mesa de Jantar')).toBe('feminine');
      expect(detectProductGender('Máquina de Lavar')).toBe('feminine');
    });

    it('should detect masculine products', () => {
      expect(detectProductGender('Computador Portátil')).toBe('masculine');
      expect(detectProductGender('Sapato de Couro')).toBe('masculine');
      expect(detectProductGender('Telemóvel Samsung')).toBe('masculine');
    });

    it('should return unknown for ambiguous products', () => {
      expect(detectProductGender('Produto Genérico')).toBe('unknown');
      expect(detectProductGender('')).toBe('unknown');
    });
  });

  describe('correctSpelling', () => {
    it('should correct common Portuguese spelling errors', () => {
      expect(correctSpelling('qualidadde')).toBe('qualidade');
      expect(correctSpelling('resistênte')).toBe('resistente');
      expect(correctSpelling('eletrônico')).toBe('eletrónico');
    });

    it('should handle text without errors', () => {
      const text = 'Produto de qualidade superior';
      expect(correctSpelling(text)).toBe(text);
    });

    it('should handle empty string', () => {
      expect(correctSpelling('')).toBe('');
    });
  });

  describe('correctGrammarAndFormatting', () => {
    it('should fix spacing issues', () => {
      const text = 'Texto  com   espaços    múltiplos';
      const result = correctGrammarAndFormatting(text);
      expect(result).toBe('Texto com espaços múltiplos');
    });

    it('should fix capitalization', () => {
      const text = 'primeira frase.segunda frase';
      const result = correctGrammarAndFormatting(text);
      expect(result).toBe('Primeira frase. Segunda frase');
    });

    it('should correct Portuguese contractions', () => {
      const text = 'de o produto em a mesa';
      const result = correctGrammarAndFormatting(text);
      expect(result).toBe('Do produto na mesa');
    });

    it('should fix gender agreement errors', () => {
      const text = 'o camisola desenvolvido especificamente';
      const result = correctGrammarAndFormatting(text);
      expect(result).toBe('A camisola desenvolvida especificamente');
    });
  });

  describe('standardizeTitle', () => {
    it('should capitalize properly', () => {
      const result = standardizeTitle('camisola em bico azul');
      expect(result).toBe('Camisola em Bico Azul');
    });

    it('should handle articles and prepositions', () => {
      const result = standardizeTitle('mesa de jantar para casa');
      expect(result).toBe('Mesa de Jantar para Casa');
    });

    it('should remove trailing punctuation', () => {
      const result = standardizeTitle('Produto Excelente!!!');
      expect(result).toBe('Produto Excelente');
    });
  });

  describe('validateProductData', () => {
    it('should validate correct product data', () => {
      const data: ProductFormData = {
        name: 'Produto Teste',
        category: 'Eletrónica',
        features: ['resistente', 'durável'],
        keywords: ['produto', 'teste'],
        targetAudience: 'Profissionais',
        additionalInfo: 'Informações extras'
      };

      const result = validateProductData(data);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should detect missing product name', () => {
      const data: ProductFormData = {
        name: '',
        category: 'Eletrónica'
      };

      const result = validateProductData(data);
      expect(result.isValid).toBe(false);
      expect(result.errors.some(e => e.includes('obrigatório'))).toBe(true);
    });

    it('should detect name too short', () => {
      const data: ProductFormData = {
        name: 'AB'
      };

      const result = validateProductData(data);
      expect(result.isValid).toBe(false);
      expect(result.errors.some(e => e.includes('3 caracteres'))).toBe(true);
    });

    it('should detect too many features', () => {
      const data: ProductFormData = {
        name: 'Produto Teste',
        features: Array(15).fill('característica')
      };

      const result = validateProductData(data);
      expect(result.isValid).toBe(false);
      expect(result.errors.some(e => e.includes('10 características'))).toBe(true);
    });
  });

  describe('validateApiOutput', () => {
    it('should validate correct API output', () => {
      const output = {
        wooCommerceMainDescription: '<p>Descrição principal com mais de 100 caracteres para passar na validação de comprimento mínimo.</p>',
        wooCommerceShortDescription: 'Descrição curta adequada',
        shortDescription: 'Meta description com exatamente 150 caracteres para testar a validação dos limites SEO estabelecidos para otimização de motores de busca.',
        slug: 'produto-teste'
      };

      const result = validateApiOutput(output);
      expect(result.isValid).toBe(true);
      expect(result.correctedContent).toBeDefined();
    });

    it('should detect missing required fields', () => {
      const output = {
        wooCommerceMainDescription: 'Descrição',
        // Missing other required fields
      };

      const result = validateApiOutput(output);
      expect(result.isValid).toBe(false);
      expect(result.errors.some(e => e.includes('obrigatórios'))).toBe(true);
    });

    it('should detect invalid object', () => {
      const result = validateApiOutput(null);
      expect(result.isValid).toBe(false);
      expect(result.errors.some(e => e.includes('objeto válido'))).toBe(true);
    });
  });
});

// Testes de integração
describe('Integration Tests', () => {
  it('should process complete workflow', () => {
    const productData: ProductFormData = {
      name: 'camisola em bico',
      category: 'vestuário',
      features: ['resistênte', 'confortavél'],
      keywords: ['camisola', 'bico'],
      targetAudience: 'mulheres jovens',
      additionalInfo: 'Ideal para o inverno'
    };

    // Validar dados
    const validation = validateProductData(productData);
    expect(validation.isValid).toBe(true);

    // Corrigir ortografia
    const correctedFeatures = productData.features?.map(f => correctSpelling(f));
    expect(correctedFeatures).toEqual(['resistente', 'confortável']);

    // Detectar género
    const gender = detectProductGender(productData.name);
    expect(gender).toBe('feminine');

    // Gerar slug
    const slug = generateSlug(productData.name);
    expect(slug).toBe('camisola-em-bico');
  });

  it('should handle edge cases gracefully', () => {
    // Teste com dados mínimos
    const minimalData: ProductFormData = {
      name: 'X'
    };

    const validation = validateProductData(minimalData);
    expect(validation.isValid).toBe(false);

    // Teste com caracteres especiais
    const specialData: ProductFormData = {
      name: 'Produto@#$%^&*()'
    };

    const slug = generateSlug(specialData.name);
    expect(slug).toBe('produto');
  });
});
