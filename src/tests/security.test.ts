/**
 * Security Test Suite
 * Comprehensive tests for security measures and configurations
 */

import { describe, it, expect, beforeEach, afterEach } from '@jest/globals';
import { 
  validateClientSideSecurity, 
  validateServerSideSecurity,
  validateApiKeyFormat,
  validatePromptIdFormat,
  isSecureEnvironment
} from '../lib/securityValidator';
import { getSecureConfig, resetConfigCache } from '../lib/secureConfig';
import { 
  getContentGenerationPrompt,
  getContentImprovementPrompt,
  getKeywordSuggestionPrompt,
  hasPromptIds
} from '../lib/securePrompts';

// Mock environment variables for testing
const originalEnv = process.env;

describe('Security Validation Tests', () => {
  beforeEach(() => {
    // Reset environment for each test
    process.env = { ...originalEnv };
    resetConfigCache();
  });

  afterEach(() => {
    // Restore original environment
    process.env = originalEnv;
    resetConfigCache();
  });

  describe('API Key Validation', () => {
    it('should validate correct API key format', () => {
      const validKey = 'sk-proj-1234567890abcdef1234567890abcdef1234567890abcdef';
      expect(validateApiKeyFormat(validKey)).toBe(true);
    });

    it('should reject invalid API key formats', () => {
      const invalidKeys = [
        'invalid-key',
        'sk-short',
        '',
        'pk-1234567890abcdef1234567890abcdef1234567890abcdef',
      ];

      invalidKeys.forEach(key => {
        expect(validateApiKeyFormat(key)).toBe(false);
      });
    });
  });

  describe('Prompt ID Validation', () => {
    it('should validate correct prompt ID format', () => {
      const validPromptId = 'pmpt_68729a6c72688195b5199cbd3862b5010b492a5c6d21e4f8';
      expect(validatePromptIdFormat(validPromptId)).toBe(true);
    });

    it('should reject invalid prompt ID formats', () => {
      const invalidPromptIds = [
        'invalid-prompt-id',
        'pmpt_short',
        'pmpt_68729a6c72688195b5199cbd3862b5010b492a5c6d21e4f8x', // too long
        'pmpt_68729a6c72688195b5199cbd3862b5010b492a5c6d21e4', // too short
        'prompt_68729a6c72688195b5199cbd3862b5010b492a5c6d21e4f8', // wrong prefix
        '',
      ];

      invalidPromptIds.forEach(id => {
        expect(validatePromptIdFormat(id)).toBe(false);
      });
    });
  });

  describe('Server-Side Security Validation', () => {
    it('should pass validation with complete configuration', () => {
      // Set up complete valid environment
      process.env.OPENAI_API_KEY = 'sk-proj-1234567890abcdef1234567890abcdef1234567890abcdef';
      process.env.OPENAI_PROMPT_ID_CONTENT_GENERATION = 'pmpt_68729a6c72688195b5199cbd3862b5010b492a5c6d21e4f8';
      process.env.OPENAI_PROMPT_ID_CONTENT_IMPROVEMENT = 'pmpt_68729a6c72688195b5199cbd3862b5010b492a5c6d21e4f8';
      process.env.OPENAI_PROMPT_ID_KEYWORD_SUGGESTION = 'pmpt_68729a6c72688195b5199cbd3862b5010b492a5c6d21e4f8';
      process.env.NODE_ENV = 'development';

      const result = validateServerSideSecurity();
      expect(result.isSecure).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should fail validation without API key in non-mock mode', () => {
      // Remove API key and disable mock mode
      delete process.env.OPENAI_API_KEY;
      process.env.MOCK_OPENAI = 'false';

      const result = validateServerSideSecurity();
      expect(result.isSecure).toBe(false);
      expect(result.errors.some(error => error.includes('No OpenAI API key'))).toBe(true);
    });

    it('should pass validation in mock mode without API key', () => {
      // Remove API key but enable mock mode
      delete process.env.OPENAI_API_KEY;
      process.env.MOCK_OPENAI = 'true';

      const result = validateServerSideSecurity();
      expect(result.isSecure).toBe(true);
    });

    it('should warn about missing prompt IDs', () => {
      // Set API key but remove prompt IDs
      process.env.OPENAI_API_KEY = 'sk-proj-1234567890abcdef1234567890abcdef1234567890abcdef';
      delete process.env.OPENAI_PROMPT_ID_CONTENT_GENERATION;

      const result = validateServerSideSecurity();
      expect(result.warnings.some(warning => warning.includes('Content generation prompt ID'))).toBe(true);
    });

    it('should fail validation with mock mode in production', () => {
      process.env.MOCK_OPENAI = 'true';
      process.env.NODE_ENV = 'production';

      const result = validateServerSideSecurity();
      expect(result.isSecure).toBe(false);
      expect(result.errors.some(error => error.includes('Mock mode enabled in production'))).toBe(true);
    });
  });

  describe('Secure Configuration Loading', () => {
    it('should load configuration with all required fields', () => {
      // Set up complete environment
      process.env.OPENAI_API_KEY = 'sk-proj-1234567890abcdef1234567890abcdef1234567890abcdef';
      process.env.OPENAI_PROMPT_ID_CONTENT_GENERATION = 'pmpt_68729a6c72688195b5199cbd3862b5010b492a5c6d21e4f8';
      process.env.OPENAI_PROMPT_ID_CONTENT_IMPROVEMENT = 'pmpt_68729a6c72688195b5199cbd3862b5010b492a5c6d21e4f8';
      process.env.OPENAI_PROMPT_ID_KEYWORD_SUGGESTION = 'pmpt_68729a6c72688195b5199cbd3862b5010b492a5c6d21e4f8';

      const config = getSecureConfig();
      
      expect(config.openai.apiKey).toBe(process.env.OPENAI_API_KEY);
      expect(config.openai.promptIds.contentGeneration).toBe(process.env.OPENAI_PROMPT_ID_CONTENT_GENERATION);
      expect(config.openai.promptIds.contentImprovement).toBe(process.env.OPENAI_PROMPT_ID_CONTENT_IMPROVEMENT);
      expect(config.openai.promptIds.keywordSuggestion).toBe(process.env.OPENAI_PROMPT_ID_KEYWORD_SUGGESTION);
    });

    it('should throw error when required variables are missing', () => {
      // Remove required variables
      delete process.env.OPENAI_API_KEY;
      delete process.env.OPENAI_PROMPT_ID_CONTENT_GENERATION;
      process.env.MOCK_OPENAI = 'false';

      expect(() => getSecureConfig()).toThrow();
    });
  });

  describe('Secure Prompt System', () => {
    beforeEach(() => {
      // Set up valid environment for prompt tests
      process.env.OPENAI_API_KEY = 'sk-proj-1234567890abcdef1234567890abcdef1234567890abcdef';
      process.env.OPENAI_PROMPT_ID_CONTENT_GENERATION = 'pmpt_68729a6c72688195b5199cbd3862b5010b492a5c6d21e4f8';
      process.env.OPENAI_PROMPT_ID_CONTENT_IMPROVEMENT = 'pmpt_68729a6c72688195b5199cbd3862b5010b492a5c6d21e4f8';
      process.env.OPENAI_PROMPT_ID_KEYWORD_SUGGESTION = 'pmpt_68729a6c72688195b5199cbd3862b5010b492a5c6d21e4f8';
    });

    it('should generate content generation prompt configuration', () => {
      const productData = {
        name: 'Test Product',
        category: 'Electronics',
        features: ['feature1', 'feature2'],
        keywords: ['keyword1', 'keyword2'],
        targetAudience: 'Tech enthusiasts',
        additionalInfo: 'Additional info'
      };

      const promptConfig = getContentGenerationPrompt(productData);
      
      expect(promptConfig.promptId).toBe('pmpt_68729a6c72688195b5199cbd3862b5010b492a5c6d21e4f8');
      expect(promptConfig.variables).toBeDefined();
      expect(promptConfig.variables?.product_name).toBe('Test Product');
    });

    it('should generate content improvement prompt configuration', () => {
      const currentDescription = 'Current product description';
      const productName = 'Test Product';

      const promptConfig = getContentImprovementPrompt(currentDescription, productName);
      
      expect(promptConfig.promptId).toBe('pmpt_68729a6c72688195b5199cbd3862b5010b492a5c6d21e4f8');
      expect(promptConfig.variables?.current_description).toBe(currentDescription);
      expect(promptConfig.variables?.product_name).toBe(productName);
    });

    it('should generate keyword suggestion prompt configuration', () => {
      const productName = 'Test Product';
      const productCategory = 'Electronics';

      const promptConfig = getKeywordSuggestionPrompt(productName, productCategory);
      
      expect(promptConfig.promptId).toBe('pmpt_68729a6c72688195b5199cbd3862b5010b492a5c6d21e4f8');
      expect(promptConfig.variables?.product_name).toBe(productName);
      expect(promptConfig.variables?.product_category).toBe(productCategory);
    });

    it('should detect when prompt IDs are available', () => {
      expect(hasPromptIds()).toBe(true);
    });

    it('should detect when prompt IDs are not available', () => {
      delete process.env.OPENAI_PROMPT_ID_CONTENT_GENERATION;
      resetConfigCache();
      
      expect(hasPromptIds()).toBe(false);
    });
  });

  describe('Environment Security', () => {
    it('should identify secure environment correctly', () => {
      // This test runs in Node.js environment (server-side)
      expect(isSecureEnvironment()).toBe(true);
    });
  });
});
