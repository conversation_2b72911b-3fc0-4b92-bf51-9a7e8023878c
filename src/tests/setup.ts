/**
 * Configuração global para testes
 */

// Configurar variáveis de ambiente para testes
process.env.NODE_ENV = 'test';
process.env.MOCK_OPENAI = 'true';

// Mock da API OpenAI para testes
jest.mock('openai', () => {
  return {
    __esModule: true,
    default: jest.fn().mockImplementation(() => ({
      chat: {
        completions: {
          create: jest.fn().mockResolvedValue({
            choices: [{
              message: {
                content: JSON.stringify({
                  wooCommerceMainDescription: '<p>Descrição principal de teste gerada pela API mock.</p>',
                  wooCommerceShortDescription: 'Descrição curta de teste',
                  shortDescription: 'Meta description de teste com exatamente 150 caracteres para validar o sistema e garantir que funciona corretamente com a API.',
                  slug: 'produto-teste-mock'
                })
              }
            }]
          })
        }
      }
    }))
  };
});

// Configurar timeout global para testes
jest.setTimeout(30000);

// Configurar console para testes (reduzir ruído)
const originalConsoleLog = console.log;
const originalConsoleWarn = console.warn;
const originalConsoleError = console.error;

beforeAll(() => {
  // Silenciar logs durante testes, exceto erros importantes
  console.log = jest.fn();
  console.warn = jest.fn();
  console.error = jest.fn((message, ...args) => {
    // Manter erros críticos visíveis
    if (typeof message === 'string' && message.includes('CRITICAL')) {
      originalConsoleError(message, ...args);
    }
  });
});

afterAll(() => {
  // Restaurar console original
  console.log = originalConsoleLog;
  console.warn = originalConsoleWarn;
  console.error = originalConsoleError;
});

// Configurar matchers customizados
expect.extend({
  toBeValidSeoContent(received) {
    const pass = (
      received &&
      typeof received === 'object' &&
      typeof received.wooCommerceMainDescription === 'string' &&
      typeof received.wooCommerceShortDescription === 'string' &&
      typeof received.shortDescription === 'string' &&
      typeof received.slug === 'string' &&
      received.wooCommerceMainDescription.length > 0 &&
      received.wooCommerceShortDescription.length > 0 &&
      received.shortDescription.length >= 140 &&
      received.shortDescription.length <= 160 &&
      received.slug.length > 0
    );

    if (pass) {
      return {
        message: () => `expected ${JSON.stringify(received)} not to be valid SEO content`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected ${JSON.stringify(received)} to be valid SEO content`,
        pass: false,
      };
    }
  },

  toBeValidSlug(received) {
    const slugRegex = /^[a-z0-9]+(?:-[a-z0-9]+)*$/;
    const pass = typeof received === 'string' && slugRegex.test(received);

    if (pass) {
      return {
        message: () => `expected ${received} not to be a valid slug`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected ${received} to be a valid slug (lowercase, hyphens only)`,
        pass: false,
      };
    }
  },

  toHaveCorrectPortugueseGrammar(received) {
    const commonErrors = [
      'o camisola',
      'este camisola',
      'um camisola',
      'o máquina',
      'este máquina',
      'um máquina',
      'desenvolvido especificamente' // para produtos femininos
    ];

    const hasErrors = commonErrors.some(error => 
      received.toLowerCase().includes(error.toLowerCase())
    );

    const pass = !hasErrors;

    if (pass) {
      return {
        message: () => `expected ${received} to have Portuguese grammar errors`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected ${received} not to have Portuguese grammar errors`,
        pass: false,
      };
    }
  }
});

// Declarações de tipos para matchers customizados
declare global {
  namespace jest {
    interface Matchers<R> {
      toBeValidSeoContent(): R;
      toBeValidSlug(): R;
      toHaveCorrectPortugueseGrammar(): R;
    }
  }
}

// Utilitários para testes
export const testUtils = {
  /**
   * Cria dados de produto mock para testes
   */
  createMockProductData: (overrides = {}) => ({
    name: 'Produto Teste',
    category: 'eletrónica',
    features: ['resistente', 'durável'],
    keywords: ['teste', 'produto'],
    targetAudience: 'profissionais',
    additionalInfo: 'Informações adicionais de teste',
    ...overrides
  }),

  /**
   * Cria conteúdo SEO mock para testes
   */
  createMockSeoContent: (overrides = {}) => ({
    wooCommerceMainDescription: '<p>Descrição principal de teste com mais de 100 caracteres para passar na validação.</p>',
    wooCommerceShortDescription: 'Descrição curta de teste',
    shortDescription: 'Meta description de teste com exatamente 150 caracteres para validar o sistema e garantir que funciona corretamente.',
    slug: 'produto-teste',
    ...overrides
  }),

  /**
   * Simula delay para testes assíncronos
   */
  delay: (ms: number) => new Promise(resolve => setTimeout(resolve, ms)),

  /**
   * Gera string aleatória para testes
   */
  randomString: (length: number = 10) => {
    const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  },

  /**
   * Valida se string é português válido (básico)
   */
  isValidPortuguese: (text: string) => {
    // Verificações básicas para português
    const hasPortugueseChars = /[àáâãäçéêëíîïóôõöúûü]/i.test(text);
    const hasValidStructure = text.length > 0 && /^[A-ZÁÀÂÃÉÊÍÓÔÕÚÇ]/.test(text);
    return hasValidStructure;
  },

  /**
   * Conta palavras em texto
   */
  countWords: (text: string) => {
    return text.trim().split(/\s+/).filter(word => word.length > 0).length;
  },

  /**
   * Remove tags HTML de texto
   */
  stripHtml: (html: string) => {
    return html.replace(/<[^>]*>/g, '').trim();
  }
};

// Configuração de mocks globais
global.fetch = jest.fn();

// Configurar Date mock para testes consistentes
const mockDate = new Date('2024-01-01T00:00:00.000Z');
jest.spyOn(global, 'Date').mockImplementation(() => mockDate as any);
Date.now = jest.fn(() => mockDate.getTime());

// Configurar Math.random mock para testes determinísticos
const mockRandom = jest.spyOn(Math, 'random');
mockRandom.mockReturnValue(0.5);

// Limpeza após cada teste
afterEach(() => {
  jest.clearAllMocks();
});

// Configuração de error handling para testes
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
});

export default {};
