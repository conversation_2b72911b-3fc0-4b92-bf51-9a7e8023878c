import { NextResponse } from 'next/server';
import OpenAI from 'openai';
import {
  getKeywordSuggestionPrompt,
  getOpenAIClientConfig,
  hasPromptIds,
  FALLBACK_PROMPTS,
  replaceFallbackPromptVariables,
  sanitizePromptVariables
} from '@/lib/securePrompts';
import { getSecureConfig } from '@/lib/secureConfig';

// Initialize OpenAI client using secure configuration
let openai: OpenAI | null = null;
let config: ReturnType<typeof getSecureConfig> | null = null;

try {
  config = getSecureConfig();

  if (!config.app.mockMode) {
    const clientConfig = getOpenAIClientConfig();
    openai = new OpenAI(clientConfig);
  }
} catch (error) {
  console.error('🚨 Failed to initialize secure OpenAI configuration for keywords:', error);
}

// Enhanced mock function for sophisticated keyword suggestions
function generateMockKeywords(productName: string, productCategory?: string): string[] {
  const cleanProductName = productName.toLowerCase().trim();
  const cleanCategory = productCategory?.toLowerCase().trim() || '';

  // Generate diverse, SEO-optimized keywords
  const keywordTemplates = [
    // Commercial intent keywords
    `comprar ${cleanProductName} online`,
    `${cleanProductName} preço portugal`,
    `${cleanProductName} melhor preço`,
    `onde comprar ${cleanProductName}`,

    // Informational keywords
    `${cleanProductName} características`,
    `${cleanProductName} avaliações`,
    `melhor ${cleanProductName}`,
    `${cleanProductName} qualidade`,

    // Category-specific keywords
    ...(cleanCategory ? [
      `${cleanCategory} ${cleanProductName}`,
      `${cleanProductName} ${cleanCategory} premium`,
      `melhor ${cleanCategory} portugal`
    ] : []),

    // Long-tail keywords
    `${cleanProductName} entrega rápida`,
    `${cleanProductName} garantia`,
    `${cleanProductName} promoção`,

    // Local SEO keywords
    `${cleanProductName} lisboa`,
    `${cleanProductName} porto`,
    `${cleanProductName} portugal`
  ];

  // Filter and select the most relevant keywords
  const relevantKeywords = keywordTemplates
    .filter(keyword => keyword.length <= 50) // Keep reasonable length
    .filter(keyword => !keyword.includes('undefined'))
    .slice(0, 8); // Get more options

  // Randomly select 3 diverse keywords
  const selectedKeywords: string[] = [];
  const usedTypes = new Set<string>();

  // Ensure we get different types of keywords
  const keywordTypes = [
    { pattern: /comprar|preço|onde/, type: 'commercial' },
    { pattern: /melhor|qualidade|características/, type: 'informational' },
    { pattern: /lisboa|porto|portugal/, type: 'local' }
  ];

  for (const keyword of relevantKeywords) {
    if (selectedKeywords.length >= 3) break;

    const keywordType = keywordTypes.find(type => type.pattern.test(keyword))?.type || 'general';

    if (!usedTypes.has(keywordType) || selectedKeywords.length < 2) {
      selectedKeywords.push(keyword);
      usedTypes.add(keywordType);
    }
  }

  // Fill remaining slots if needed
  while (selectedKeywords.length < 3 && relevantKeywords.length > selectedKeywords.length) {
    const remaining = relevantKeywords.filter(k => !selectedKeywords.includes(k));
    if (remaining.length > 0) {
      selectedKeywords.push(remaining[0]);
    } else {
      break;
    }
  }

  return selectedKeywords.slice(0, 3);
}

export async function POST(request: Request) {
  try {
    const { productName, productCategory } = await request.json();

    if (!productName) {
      return NextResponse.json({ error: 'O nome do produto é obrigatório.' }, { status: 400 });
    }

    // Use mock mode if enabled
    const isInMockMode = config?.app.mockMode || process.env.MOCK_OPENAI === 'true';

    if (isInMockMode) {
      const keywords = generateMockKeywords(productName, productCategory);
      await new Promise(resolve => setTimeout(resolve, 500)); // Simulate delay
      return NextResponse.json({ keywords });
    }

    if (!openai) {
      return NextResponse.json(
        { error: 'A API da OpenAI não está configurada ou ative o modo mock.' },
        { status: 500 }
      );
    }

    // Use secure prompt system
    let response: any;

    if (hasPromptIds()) {
      // Usar prompt IDs seguros
      const promptConfig = getKeywordSuggestionPrompt(productName, productCategory);

      console.log('🔒 Using secure keyword prompt ID:', promptConfig.promptId);
      console.log('📝 Keyword variables:', sanitizePromptVariables(promptConfig.variables || {}));

      const userPrompt = replaceFallbackPromptVariables(
        FALLBACK_PROMPTS.keywordSuggestion,
        promptConfig.variables || {}
      );

      response = await openai.chat.completions.create({
        model: "gpt-4o",
        messages: [
          {
            role: "user",
            content: userPrompt
          }
        ],
        temperature: 0.7,
        max_tokens: 200,
        response_format: { type: "json_object" },
        top_p: 0.9,
        frequency_penalty: 0.1,
        presence_penalty: 0.1
      });
    } else {
      // Fallback para prompts tradicionais
      console.log('⚠️ Using fallback keyword prompts');

      const promptConfig = getKeywordSuggestionPrompt(productName, productCategory);
      const userPrompt = replaceFallbackPromptVariables(
        FALLBACK_PROMPTS.keywordSuggestion,
        promptConfig.variables || {}
      );

      response = await openai.chat.completions.create({
        model: "gpt-4o",
        messages: [
          {
            role: "user",
            content: userPrompt
          }
        ],
        temperature: 0.7,
        max_tokens: 200,
        response_format: { type: "json_object" },
        top_p: 0.9,
        frequency_penalty: 0.1,
        presence_penalty: 0.1
      });
    }

    const content = response.choices[0].message.content;
    if (!content) {
      throw new Error("A resposta da API está vazia.");
    }

    const parsedContent = JSON.parse(content);
    return NextResponse.json(parsedContent);

  } catch (error) {
    console.error('Erro na API de sugestão de palavras-chave:', error);
    return NextResponse.json({ error: 'Falha ao sugerir palavras-chave.' }, { status: 500 });
  }
}
