import { NextResponse } from 'next/server';
import { seoContentCache } from '@/lib/cacheSystem';

/**
 * Endpoint para estatísticas do cache
 * GET /api/cache-stats
 */
export async function GET() {
  try {
    const stats = seoContentCache.getStats();
    
    // Calcular informações adicionais
    const now = Date.now();
    const oldestAgeMinutes = stats.oldestEntry ? Math.floor((now - stats.oldestEntry) / (1000 * 60)) : 0;
    const newestAgeMinutes = stats.newestEntry ? Math.floor((now - stats.newestEntry) / (1000 * 60)) : 0;
    
    const response = {
      cache: {
        size: stats.size,
        maxSize: stats.maxSize,
        usage: `${stats.size}/${stats.maxSize} (${Math.round((stats.size / stats.maxSize) * 100)}%)`,
        hitRate: `${Math.round(stats.hitRate * 100)}%`,
        totalHits: stats.totalHits
      },
      entries: {
        oldest: {
          ageMinutes: oldestAgeMinutes,
          ageHours: Math.round(oldestAgeMinutes / 60 * 10) / 10,
          timestamp: stats.oldestEntry
        },
        newest: {
          ageMinutes: newestAgeMinutes,
          ageHours: Math.round(newestAgeMinutes / 60 * 10) / 10,
          timestamp: stats.newestEntry
        }
      },
      performance: {
        status: stats.hitRate > 0.5 ? 'excellent' : stats.hitRate > 0.3 ? 'good' : 'needs_improvement',
        recommendation: stats.hitRate > 0.5 
          ? 'Cache funcionando otimamente' 
          : stats.hitRate > 0.3 
            ? 'Cache com boa performance' 
            : 'Considere ajustar configurações do cache'
      },
      timestamp: now
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Erro ao obter estatísticas do cache:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

/**
 * Endpoint para limpar o cache
 * DELETE /api/cache-stats
 */
export async function DELETE() {
  try {
    seoContentCache.clear();
    
    return NextResponse.json({
      message: 'Cache limpo com sucesso',
      timestamp: Date.now()
    });
  } catch (error) {
    console.error('Erro ao limpar cache:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
